[2025-07-28 15:53:08] [Flask] [INFO] tkinter模块导入成功
[2025-07-28 15:53:08] [Flask] [INFO] tkinter根窗口创建成功
[2025-07-28 15:53:08] [Flask] [INFO] 窗口属性设置成功
[2025-07-28 15:53:08] [wxauto] [INFO] 自动启动配置: 启用=False, 倒计时=5秒
[2025-07-28 15:53:08] [wxauto] [INFO] 从配置文件加载配置成功
[2025-07-28 15:53:08] [wxauto] [INFO] 统一日志系统已初始化
[2025-07-28 15:53:08] [wxauto] [INFO] 应用实例创建成功
[2025-07-28 15:53:08] [wxauto] [INFO] 开始启动UI主循环
[2025-07-28 15:53:08] [wxauto] [INFO] 自动启动已禁用，请手动启动服务
[2025-07-28 15:53:38] [wxauto] [INFO] wxautox库在打包环境中可用
[2025-07-28 20:05:41] [wxauto] [INFO] UI主循环已结束
[2025-07-28 22:38:09] [Flask] [INFO] tkinter模块导入成功
[2025-07-28 22:38:09] [Flask] [INFO] tkinter根窗口创建成功
[2025-07-28 22:38:09] [Flask] [INFO] 窗口属性设置成功
[2025-07-28 22:38:09] [wxauto] [INFO] 自动启动配置: 启用=False, 倒计时=5秒
[2025-07-28 22:38:09] [wxauto] [INFO] 从配置文件加载配置成功
[2025-07-28 22:38:09] [wxauto] [INFO] 统一日志系统已初始化
[2025-07-28 22:38:09] [wxauto] [INFO] 应用实例创建成功
[2025-07-28 22:38:09] [wxauto] [INFO] 开始启动UI主循环
[2025-07-28 22:38:10] [wxauto] [INFO] 自动启动已禁用，请手动启动服务
[2025-07-28 22:38:40] [wxauto] [INFO] wxautox库在打包环境中可用
[2025-07-28 22:42:19] [wxauto] [INFO] 自动启动配置已保存: 启用=True, 倒计时=5秒
[2025-07-28 22:42:19] [wxauto] [INFO] 已启用自动启动，将在 5 秒后启动服务
[2025-07-28 22:42:19] [wxauto] [INFO] 【自动启动】5 秒后启动服务...
[2025-07-28 22:42:21] [wxauto] [INFO] 【自动启动】3 秒后启动服务...
[2025-07-28 22:42:22] [wxauto] [INFO] 【自动启动】2 秒后启动服务...
[2025-07-28 22:42:23] [wxauto] [INFO] 【自动启动】1 秒后启动服务...
[2025-07-28 22:42:24] [wxauto] [INFO] 【自动启动】倒计时结束，准备启动服务...
[2025-07-28 22:42:24] [wxauto] [INFO] 【自动启动】检测到 wxauto 已安装
[2025-07-28 22:42:24] [wxauto] [INFO] 【自动启动】正在启动服务...
[2025-07-28 22:42:24] [wxauto] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug
[2025-07-28 22:42:24] [wxauto] [INFO] 已设置控制台代码页为UTF-8 (65001)
[2025-07-28 22:42:24] [wxauto] [INFO] API服务已启动，监听地址: 0.0.0.0:5000
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)]
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug']
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 正在启动API服务...
[2025-07-28 22:42:24] [wxauto] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\comtypes\\gen\\__init__.py'>
[2025-07-28 22:42:24] [wxauto] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311'
[2025-07-28 22:42:24] [wxauto] [INFO] 2025-07-28 22:42:24 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 22:42:24] [wxauto] [INFO] 2025-07-28 22:42:24 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 22:42:24] [wxauto] [INFO] 2025-07-28 22:42:24 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 22:42:24] [wxauto] [INFO] 2025-07-28 22:42:24 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 22:42:25] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:42:25] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:42:25] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:42:25] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:42:25] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:42:25] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:42:25] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:42:25] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:42:25] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:42:25] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:42:25] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:42:25] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:42:25] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:42:25] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:42:25] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:42:25] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:42:25] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:25] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:42:25] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:42:25] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:42:25] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:42:25] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:42:25] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:42:25] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:42:25] [Flask] [INFO] 调试模式: True
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 调试模式: True
[2025-07-28 22:42:25] [wxauto] [INFO] * Serving Flask app 'app'
[2025-07-28 22:42:25] [wxauto] [INFO] * Debug mode: on
[2025-07-28 22:42:26] [wxauto] [INFO] 正在自动初始化微信...
[2025-07-28 22:42:27] [wxauto] [INFO] 微信初始化尝试 1/3...
[2025-07-28 22:42:29] [wxauto] [INFO] 微信自动初始化请求失败: Expecting value: line 1 column 1 (char 0)
[2025-07-28 22:42:29] [wxauto] [INFO] 将在 2 秒后重试...
[2025-07-28 22:42:25] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 22:42:25)
[2025-07-28 22:42:31] [wxauto] [INFO] 微信初始化尝试 2/3...
[2025-07-28 22:42:35] [wxauto] [INFO] 微信初始化尝试 3/3...
[2025-07-28 22:42:29] [wxauto] [INFO] 将在 2 秒后重试... (重复 2 次，最后: 2025-07-28 22:42:33)
[2025-07-28 22:42:29] [wxauto] [INFO] 微信自动初始化请求失败: Expecting value: line 1 column 1 (char 0) (重复 3 次，最后: 2025-07-28 22:42:37)
[2025-07-28 22:43:15] [wxauto] [INFO] API服务已停止
[2025-07-28 22:43:19] [wxauto] [INFO] 2025-07-28 22:43:19 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 22:43:19] [wxauto] [INFO] 2025-07-28 22:43:19 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 22:43:19] [wxauto] [INFO] 2025-07-28 22:43:19 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 22:43:19] [wxauto] [INFO] 2025-07-28 22:43:19 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 22:43:19] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:43:19] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:43:19] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:43:19] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:43:19] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:43:19] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:43:19] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:43:19] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:43:19] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:43:19] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:43:19] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:43:19] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:43:19] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:43:19] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:43:19] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:43:19] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:43:19] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:43:19] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:43:19] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:43:19] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:43:19] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:43:19] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:43:19] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:43:19] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:43:19] [Flask] [INFO] 调试模式: True
[2025-07-28 22:43:23] [wxauto] [INFO] 微信自动初始化请求失败: Expecting value: line 1 column 1 (char 0)
[2025-07-28 22:43:23] [wxauto] [INFO] 将在 2 秒后重试...
[2025-07-28 22:42:24] [wxauto] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] 已设置控制台代码页为UTF-8 (65001) (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] API服务已启动，监听地址: 0.0.0.0:5000 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)] (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor'] (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor'] (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug'] (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化 (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] __main__ - INFO - 正在启动API服务... (重复 2 次，最后: 2025-07-28 22:43:18)
[2025-07-28 22:42:24] [wxauto] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\comtypes\\gen\\__init__.py'> (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:24] [wxauto] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311' (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] ===== 开始启动API服务 ===== (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在检查互斥锁... (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] API服务端口: 5000 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 互斥锁检查通过 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在检查依赖项... (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 依赖项检查成功 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 依赖项检查通过 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在启动队列处理器... (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 队列处理器已启动 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 队列处理器启动成功 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor'] (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数... (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在创建Flask应用... (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 成功创建Flask应用 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 正在启动Flask应用... (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000 (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] [Flask] [INFO] 调试模式: True (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] * Serving Flask app 'app' (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:25] [wxauto] [INFO] * Debug mode: on (重复 2 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:43:19] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 22:43:19)
[2025-07-28 22:42:26] [wxauto] [INFO] 正在自动初始化微信... (重复 2 次，最后: 2025-07-28 22:43:20)
[2025-07-28 22:42:27] [wxauto] [INFO] 微信初始化尝试 1/3... (重复 2 次，最后: 2025-07-28 22:43:21)
[2025-07-28 22:42:31] [wxauto] [INFO] 微信初始化尝试 2/3... (重复 2 次，最后: 2025-07-28 22:43:25)
[2025-07-28 22:43:23] [wxauto] [INFO] 将在 2 秒后重试... (重复 2 次，最后: 2025-07-28 22:43:27)
[2025-07-28 22:42:35] [wxauto] [INFO] 微信初始化尝试 3/3... (重复 2 次，最后: 2025-07-28 22:43:29)
[2025-07-28 22:43:23] [wxauto] [INFO] 微信自动初始化请求失败: Expecting value: line 1 column 1 (char 0) (重复 3 次，最后: 2025-07-28 22:43:31)
[2025-07-28 22:45:41] [wxauto] [INFO] 已在浏览器中打开API文档: http://localhost:5000/api-docs
[2025-07-28 22:45:57] [wxauto] [INFO] 已在浏览器中打开日志查看页面: http://localhost:5000/logs
[2025-07-28 22:46:23] [wxauto] [INFO] API服务已停止
[2025-07-28 22:46:25] [wxauto] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug
[2025-07-28 22:46:26] [wxauto] [INFO] 已设置控制台代码页为UTF-8 (65001)
[2025-07-28 22:46:26] [wxauto] [INFO] API服务已启动，监听地址: 0.0.0.0:5000
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)]
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug']
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:46:26] [wxauto] [INFO] __main__ - INFO - 正在启动API服务...
[2025-07-28 22:46:26] [wxauto] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\comtypes\\gen\\__init__.py'>
[2025-07-28 22:46:26] [wxauto] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311'
[2025-07-28 22:46:26] [wxauto] [INFO] 2025-07-28 22:46:26 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 22:46:26] [wxauto] [INFO] 2025-07-28 22:46:26 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 22:46:26] [wxauto] [INFO] 2025-07-28 22:46:26 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 22:46:26] [wxauto] [INFO] 2025-07-28 22:46:26 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 22:46:26] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:46:26] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:46:26] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:46:26] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:46:26] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:46:26] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:46:26] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:46:26] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:46:26] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:46:26] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:46:26] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:46:26] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:46:26] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:46:26] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:46:26] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:46:26] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:46:26] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:46:26] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:46:26] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:46:26] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:46:26] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:46:26] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:46:26] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:46:26] [Flask] [INFO] 调试模式: True
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI91882\\setuptools\\_vendor']
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:46:26] [wxauto] [INFO] [Flask] [INFO] 调试模式: True
[2025-07-28 22:46:26] [wxauto] [INFO] * Serving Flask app 'app'
[2025-07-28 22:46:26] [wxauto] [INFO] * Debug mode: on
[2025-07-28 22:46:28] [wxauto] [INFO] 正在自动初始化微信...
[2025-07-28 22:46:29] [wxauto] [INFO] 微信初始化尝试 1/3...
[2025-07-28 22:46:31] [wxauto] [INFO] 微信自动初始化请求失败: Expecting value: line 1 column 1 (char 0)
[2025-07-28 22:46:31] [wxauto] [INFO] 将在 2 秒后重试...
[2025-07-28 22:46:31] [wxauto] [INFO] 自动启动配置已保存: 启用=False, 倒计时=5秒
[2025-07-28 22:46:31] [wxauto] [INFO] 已禁用自动启动
[2025-07-28 22:46:26] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 22:46:26)
[2025-07-28 22:46:33] [wxauto] [INFO] 微信初始化尝试 2/3...
[2025-07-28 22:46:37] [wxauto] [INFO] 微信初始化尝试 3/3...
[2025-07-28 22:46:31] [wxauto] [INFO] 将在 2 秒后重试... (重复 2 次，最后: 2025-07-28 22:46:35)
[2025-07-28 22:46:31] [wxauto] [INFO] 微信自动初始化请求失败: Expecting value: line 1 column 1 (char 0) (重复 3 次，最后: 2025-07-28 22:46:39)
[2025-07-28 22:48:04] [wxauto] [INFO] 已在浏览器中打开API文档: http://localhost:5000/api-docs
[2025-07-28 22:50:28] [wxauto] [INFO] 库选择变更: wxautox
[2025-07-28 22:50:28] [wxauto] [INFO] 正在加载配置文件...
[2025-07-28 22:50:28] [wxauto] [INFO] 当前配置: {'api_keys': ['test-key-2'], 'port': 5000, 'wechat_lib': 'wxauto', 'auto_start_enabled': False, 'auto_start_countdown': 5}
[2025-07-28 22:50:28] [wxauto] [INFO] 更新库配置: wxauto -> wxautox
[2025-07-28 22:50:28] [wxauto] [INFO] 正在保存配置文件...
[2025-07-28 22:50:28] [wxauto] [INFO] 配置保存成功并验证通过: wxautox
[2025-07-28 22:50:28] [wxauto] [INFO] 配置已保存到JSON配置文件（.env文件已弃用）
[2025-07-28 22:50:28] [wxautox] [INFO] 已更新微信库配置: wxautox
[2025-07-28 22:50:36] [wxautox] [INFO] API服务已停止
[2025-07-28 22:50:36] [wxauto] [INFO] UI主循环已结束
[2025-07-28 22:51:50] [Flask] [INFO] tkinter模块导入成功
[2025-07-28 22:51:50] [Flask] [INFO] tkinter根窗口创建成功
[2025-07-28 22:51:50] [Flask] [INFO] 窗口属性设置成功
[2025-07-28 22:51:50] [wxautox] [INFO] 自动启动配置: 启用=False, 倒计时=5秒
[2025-07-28 22:51:50] [wxautox] [INFO] 从配置文件加载配置成功
[2025-07-28 22:51:50] [wxautox] [INFO] 统一日志系统已初始化
[2025-07-28 22:51:50] [wxautox] [INFO] 应用实例创建成功
[2025-07-28 22:51:50] [wxautox] [INFO] 开始启动UI主循环
[2025-07-28 22:51:50] [wxautox] [INFO] 自动启动已禁用，请手动启动服务
[2025-07-28 22:51:54] [wxautox] [INFO] wxautox库在打包环境中可用
[2025-07-28 22:51:54] [wxautox] [INFO] 检查wxautox可用性...
[2025-07-28 22:51:54] [wxautox] [INFO] wxautox可用，继续启动服务
[2025-07-28 22:51:54] [wxautox] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug
[2025-07-28 22:51:54] [wxautox] [INFO] 已设置控制台代码页为UTF-8 (65001)
[2025-07-28 22:51:54] [wxautox] [INFO] API服务已启动，监听地址: 0.0.0.0:5000
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)]
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\setuptools\\_vendor']
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\setuptools\\_vendor']
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug']
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:51:55] [wxautox] [INFO] __main__ - INFO - 正在启动API服务...
[2025-07-28 22:51:55] [wxautox] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\comtypes\\gen\\__init__.py'>
[2025-07-28 22:51:55] [wxautox] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311'
[2025-07-28 22:51:55] [wxautox] [INFO] 2025-07-28 22:51:55 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 22:51:55] [wxautox] [INFO] 2025-07-28 22:51:55 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 22:51:55] [wxautox] [INFO] 2025-07-28 22:51:55 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 22:51:55] [wxautox] [INFO] 2025-07-28 22:51:55 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 22:51:55] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:51:55] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:51:55] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:51:55] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:51:55] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:51:55] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:51:55] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:51:55] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:51:55] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:51:55] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:51:55] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:51:55] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:51:55] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:51:55] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:51:55] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:51:55] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:51:55] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\setuptools\\_vendor']
[2025-07-28 22:51:55] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:51:55] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:51:55] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:51:55] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:51:55] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:51:55] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:51:55] [Flask] [INFO] 调试模式: True
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI67402\\setuptools\\_vendor']
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:51:55] [wxautox] [INFO] [Flask] [INFO] 调试模式: True
[2025-07-28 22:51:55] [wxautox] [INFO] * Serving Flask app 'app'
[2025-07-28 22:51:55] [wxautox] [INFO] * Debug mode: on
[2025-07-28 22:51:56] [wxautox] [INFO] 正在自动初始化微信...
[2025-07-28 22:51:57] [wxautox] [INFO] 微信初始化尝试 1/3...
[2025-07-28 22:51:59] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 22:51:59] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 22:51:59] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 22:51:59] [Flask] [INFO] 尝试导入wxautox库
[2025-07-28 22:51:59] [Flask] [INFO] 打包环境中直接尝试导入wxautox库
[2025-07-28 22:51:59] [Flask] [INFO] 成功导入wxautox库（打包环境直接导入）
[2025-07-28 22:51:59] [Flask] [INFO] 使用微信自动化库: wxautox
[2025-07-28 22:52:00] [wxautox] [INFO] 微信自动初始化请求失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
[2025-07-28 22:52:00] [wxautox] [INFO] 将在 2 秒后重试...
[2025-07-28 22:51:55] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 22:51:55)
[2025-07-28 22:52:02] [wxautox] [INFO] 微信初始化尝试 2/3...
[2025-07-28 22:52:06] [wxautox] [INFO] 微信初始化尝试 3/3...
[2025-07-28 22:52:00] [wxautox] [INFO] 将在 2 秒后重试... (重复 2 次，最后: 2025-07-28 22:52:04)
[2025-07-28 22:51:59] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 3 次，最后: 2025-07-28 22:52:08)
[2025-07-28 22:52:00] [wxautox] [INFO] 微信自动初始化请求失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')) (重复 3 次，最后: 2025-07-28 22:52:08)
[2025-07-28 22:52:15] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 22:52:15] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 22:52:15] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 22:52:15] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.01秒
[2025-07-28 22:52:16] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 22:52:16] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 22:52:19] [Flask] [INFO] [wxautox] 获取二维码请求参数: wxpath=None
[2025-07-28 22:52:19] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 22:52:19] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 22:52:19] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 22:52:19] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 22:52:19] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 22:52:19] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 22:52:19] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 22:52:16] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 4 次，最后: 2025-07-28 22:52:22)
[2025-07-28 22:52:27] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 22:52:47] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 22:52:15] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 22:52:46)
[2025-07-28 22:52:15] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 22:52:46)
[2025-07-28 22:52:15] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 22:52:46)
[2025-07-28 22:52:27] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 22:52:49)
[2025-07-28 22:52:54] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 22:53:00] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 22:53:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 22:52:54] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 22:53:06)
[2025-07-28 22:56:14] [wxautox] [INFO] API服务已停止
[2025-07-28 22:56:14] [wxautox] [INFO] UI主循环已结束
[2025-07-28 22:59:13] [Flask] [INFO] tkinter模块导入成功
[2025-07-28 22:59:13] [Flask] [INFO] tkinter根窗口创建成功
[2025-07-28 22:59:13] [Flask] [INFO] 窗口属性设置成功
[2025-07-28 22:59:13] [wxautox] [INFO] 自动启动配置: 启用=False, 倒计时=5秒
[2025-07-28 22:59:13] [wxautox] [INFO] 从配置文件加载配置成功
[2025-07-28 22:59:13] [wxautox] [INFO] 统一日志系统已初始化
[2025-07-28 22:59:13] [wxautox] [INFO] 应用实例创建成功
[2025-07-28 22:59:13] [wxautox] [INFO] 开始启动UI主循环
[2025-07-28 22:59:14] [wxautox] [INFO] 自动启动已禁用，请手动启动服务
[2025-07-28 22:59:17] [wxautox] [INFO] wxautox库在打包环境中可用
[2025-07-28 22:59:17] [wxautox] [INFO] 检查wxautox可用性...
[2025-07-28 22:59:17] [wxautox] [INFO] wxautox可用，继续启动服务
[2025-07-28 22:59:17] [wxautox] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug
[2025-07-28 22:59:18] [wxautox] [INFO] 已设置控制台代码页为UTF-8 (65001)
[2025-07-28 22:59:18] [wxautox] [INFO] API服务已启动，监听地址: 0.0.0.0:5000
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)]
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug']
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:59:18] [wxautox] [INFO] __main__ - INFO - 正在启动API服务...
[2025-07-28 22:59:18] [wxautox] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\comtypes\\gen\\__init__.py'>
[2025-07-28 22:59:18] [wxautox] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311'
[2025-07-28 22:59:18] [wxautox] [INFO] 2025-07-28 22:59:18 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 22:59:18] [wxautox] [INFO] 2025-07-28 22:59:18 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 22:59:18] [wxautox] [INFO] 2025-07-28 22:59:18 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 22:59:18] [wxautox] [INFO] 2025-07-28 22:59:18 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 22:59:18] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:59:18] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:59:18] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:59:18] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:59:18] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:59:18] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:59:18] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:59:18] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:59:18] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:59:18] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:59:18] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:59:18] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:59:18] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:59:18] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:59:18] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:59:18] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:59:18] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 22:59:18] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:59:18] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:59:18] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:59:18] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:59:18] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:59:18] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:59:18] [Flask] [INFO] 调试模式: True
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] API服务端口: 5000
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 依赖项检查成功
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 依赖项检查通过
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 队列处理器已启动
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 22:59:18] [wxautox] [INFO] [Flask] [INFO] 调试模式: True
[2025-07-28 22:59:18] [wxautox] [INFO] * Serving Flask app 'app'
[2025-07-28 22:59:18] [wxautox] [INFO] * Debug mode: on
[2025-07-28 22:59:20] [wxautox] [INFO] 正在自动初始化微信...
[2025-07-28 22:59:20] [wxautox] [INFO] 库选择变更: wxauto
[2025-07-28 22:59:20] [wxautox] [INFO] 正在加载配置文件...
[2025-07-28 22:59:20] [wxautox] [INFO] 当前配置: {'api_keys': ['test-key-2'], 'port': 5000, 'wechat_lib': 'wxautox', 'auto_start_enabled': False, 'auto_start_countdown': 5}
[2025-07-28 22:59:20] [wxautox] [INFO] 更新库配置: wxautox -> wxauto
[2025-07-28 22:59:20] [wxautox] [INFO] 正在保存配置文件...
[2025-07-28 22:59:20] [wxautox] [INFO] 配置保存成功并验证通过: wxauto
[2025-07-28 22:59:20] [wxautox] [INFO] 配置已保存到JSON配置文件（.env文件已弃用）
[2025-07-28 22:59:20] [wxauto] [INFO] 已更新微信库配置: wxauto
[2025-07-28 22:59:21] [wxauto] [INFO] 微信初始化尝试 1/3...
[2025-07-28 22:59:23] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 22:59:23] [Flask] [INFO] 尝试导入wxautox库
[2025-07-28 22:59:23] [Flask] [INFO] 打包环境中直接尝试导入wxautox库
[2025-07-28 22:59:23] [Flask] [INFO] 成功导入wxautox库（打包环境直接导入）
[2025-07-28 22:59:23] [Flask] [INFO] 使用微信自动化库: wxautox
[2025-07-28 22:59:23] [wxauto] [INFO] 微信自动初始化请求失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
[2025-07-28 22:59:23] [wxauto] [INFO] 将在 2 秒后重试...
[2025-07-28 22:59:24] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 22:59:24] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 22:59:18] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 22:59:18)
[2025-07-28 22:59:25] [wxauto] [INFO] 微信初始化尝试 2/3...
[2025-07-28 22:59:29] [wxauto] [INFO] 已在浏览器中打开API文档: http://localhost:5000/api-docs
[2025-07-28 22:59:29] [wxauto] [INFO] 微信初始化尝试 3/3...
[2025-07-28 22:59:23] [wxauto] [INFO] 将在 2 秒后重试... (重复 2 次，最后: 2025-07-28 22:59:27)
[2025-07-28 22:59:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 22:59:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 22:59:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 22:59:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 22:59:23] [wxauto] [INFO] 微信自动初始化请求失败: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')) (重复 3 次，最后: 2025-07-28 22:59:31)
[2025-07-28 22:59:23] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 11 次，最后: 2025-07-28 22:59:39)
[2025-07-28 22:59:44] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 22:59:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:00:04)
[2025-07-28 22:59:34] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:00:04)
[2025-07-28 22:59:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:00:04)
[2025-07-28 22:59:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:00:05)
[2025-07-28 22:59:44] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 23:00:06)
[2025-07-28 23:00:12] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:00:12] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 4 次，最后: 2025-07-28 23:00:17)
[2025-07-28 23:00:22] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:00:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:00:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:00:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:00:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:00:46] [Flask] [INFO] [wxautox] 获取二维码请求参数: wxpath=None
[2025-07-28 23:00:46] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:00:46] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:00:46] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:00:46] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:00:46] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:00:46] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 23:00:46] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:00:47] [Flask] [INFO] [wxautox] 自动登录请求参数: wxpath=None, timeout=10
[2025-07-28 23:00:22] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 23:00:44)
[2025-07-28 23:00:50] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:00:51] [Flask] [ERROR] [wxautox] 获取登录二维码失败: 'WeChatLoginWnd' object has no attribute 'control'
[2025-07-28 23:00:46] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:46] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:46] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:46] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:46] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:46] [Flask] [DEBUG] 微信文件大小: 660128 bytes (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:46] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:00:53] [Flask] [ERROR] [wxautox] 自动登录失败: 'WeChatLoginWnd' object has no attribute 'control'
[2025-07-28 23:01:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 尝试导入wxautox库
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 打包环境中直接尝试导入wxautox库
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 成功导入wxautox库（打包环境直接导入）
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 使用微信自动化库: wxautox
[2025-07-28 23:01:06] [wxauto] [INFO] 
[2025-07-28 23:01:06] [wxauto] [INFO] >>> 未授权设备，获取授权：https://plus.wxauto.org
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 22:59:18)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 11 次，最后: 2025-07-28 22:59:39)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:00:04)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:00:04)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:00:04)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:00:05)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 23:00:06)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 4 次，最后: 2025-07-28 23:00:17)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] [wxautox] 获取二维码请求参数: wxpath=None
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] [wxautox] 自动登录请求参数: wxpath=None, timeout=10
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 23:00:44)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [ERROR] [wxautox] 获取登录二维码失败: 'WeChatLoginWnd' object has no attribute 'control'
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [DEBUG] 微信文件大小: 660128 bytes (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe (重复 2 次，最后: 2025-07-28 23:00:47)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [ERROR] [wxautox] 自动登录失败: 'WeChatLoginWnd' object has no attribute 'control'
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:00:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:01:04)
[2025-07-28 23:00:34] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:01:04)
[2025-07-28 23:00:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:01:04)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 5 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:06] [wxauto] [INFO]  (重复 170 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:06] [wxauto] [INFO] >>> 未授权设备，获取授权：https://plus.wxauto.org (重复 42 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:01:06)
[2025-07-28 23:01:16] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:01:16] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:01:16] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:00:50] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 23:01:11)
[2025-07-28 23:01:17] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:00:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:01:17)
[2025-07-28 23:01:17] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 4 次，最后: 2025-07-28 23:01:22)
[2025-07-28 23:01:27] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:01:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:01:16] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:01:34)
[2025-07-28 23:01:16] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:01:34)
[2025-07-28 23:01:16] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:01:34)
[2025-07-28 23:01:27] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 10 次，最后: 2025-07-28 23:01:49)
[2025-07-28 23:01:55] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:02:00] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:02:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:02:04] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:01:55] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 4 次，最后: 2025-07-28 23:02:00)
[2025-07-28 23:02:06] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:02:00] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:02:04)
[2025-07-28 23:02:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:02:04)
[2025-07-28 23:01:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:02:05)
[2025-07-28 23:02:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:02:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:02:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:02:04] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:02:34)
[2025-07-28 23:02:06] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 14 次，最后: 2025-07-28 23:02:38)
[2025-07-28 23:02:44] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:02:44] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 4 次，最后: 2025-07-28 23:02:49)
[2025-07-28 23:02:55] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:03:04] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:02:34] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:03:04)
[2025-07-28 23:02:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:03:04)
[2025-07-28 23:02:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:03:05)
[2025-07-28 23:03:24] [wxauto] [INFO] 自动启动配置已保存: 启用=True, 倒计时=5秒
[2025-07-28 23:03:26] [wxauto] [INFO] 正在重载配置...
[2025-07-28 23:03:28] [Flask] [INFO] 配置已重新加载
[2025-07-28 23:03:28] [wxauto] [INFO] 配置重载成功
[2025-07-28 23:03:32] [wxauto] [INFO] >>> 未授权设备，获取授权：https://plus.wxauto.org
[2025-07-28 23:03:32] [wxauto] [INFO] API服务已停止
[2025-07-28 23:03:35] [wxauto] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug
[2025-07-28 23:03:35] [wxauto] [INFO] 已设置控制台代码页为UTF-8 (65001)
[2025-07-28 23:03:35] [wxauto] [INFO] API服务已启动，监听地址: 0.0.0.0:5000
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)]
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug']
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 正在启动API服务...
[2025-07-28 23:03:35] [wxauto] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\comtypes\\gen\\__init__.py'>
[2025-07-28 23:03:35] [wxauto] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311'
[2025-07-28 23:03:36] [wxauto] [INFO] 2025-07-28 23:03:36 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 23:03:36] [wxauto] [INFO] 2025-07-28 23:03:36 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 23:03:36] [wxauto] [INFO] 2025-07-28 23:03:36 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 23:03:36] [wxauto] [INFO] 2025-07-28 23:03:36 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 23:03:36] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 23:03:36] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 23:03:36] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 23:03:36] [Flask] [INFO] API服务端口: 5000
[2025-07-28 23:03:36] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 23:03:36] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 23:03:36] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 23:03:36] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 23:03:36] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 23:03:36] [Flask] [INFO] 依赖项检查成功
[2025-07-28 23:03:36] [Flask] [INFO] 依赖项检查通过
[2025-07-28 23:03:36] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 23:03:36] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 23:03:36] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 23:03:36] [Flask] [INFO] 队列处理器已启动
[2025-07-28 23:03:36] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 23:03:36] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:36] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 23:03:36] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 23:03:36] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 23:03:36] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 23:03:36] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 23:03:36] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 23:03:36] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 23:03:36] [Flask] [INFO] 调试模式: True
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] API服务端口: 5000
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 依赖项检查成功
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 依赖项检查通过
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 队列处理器已启动
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 调试模式: True
[2025-07-28 23:03:36] [wxauto] [INFO] * Serving Flask app 'app'
[2025-07-28 23:03:36] [wxauto] [INFO] * Debug mode: on
[2025-07-28 23:03:37] [wxauto] [INFO] 正在自动初始化微信...
[2025-07-28 23:03:38] [wxauto] [INFO] 微信初始化尝试 1/3...
[2025-07-28 23:03:39] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:03:39] [Flask] [INFO] 尝试导入wxauto库
[2025-07-28 23:03:39] [Flask] [INFO] 打包环境中直接尝试导入wxauto库
[2025-07-28 23:03:39] [Flask] [INFO] 成功导入wxauto库（打包环境直接导入）
[2025-07-28 23:03:39] [Flask] [INFO] 使用微信自动化库: wxauto
[2025-07-28 23:03:39] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:03:39] [wxauto] [INFO] [Flask] [INFO] 尝试导入wxauto库
[2025-07-28 23:03:39] [wxauto] [INFO] [Flask] [INFO] 打包环境中直接尝试导入wxauto库
[2025-07-28 23:03:39] [wxauto] [INFO] [Flask] [INFO] 成功导入wxauto库（打包环境直接导入）
[2025-07-28 23:03:39] [wxauto] [INFO] [Flask] [INFO] 使用微信自动化库: wxauto
[2025-07-28 23:03:39] [wxauto] [INFO] 初始化成功，获取到已登录窗口：Y
[2025-07-28 23:03:39] [Flask] [INFO] 成功创建wxauto.WeChat实例
[2025-07-28 23:03:39] [Flask] [INFO] 微信实例初始化成功，但无法获取窗口名称，使用库: wxauto
[2025-07-28 23:03:39] [Flask] [INFO] 正在打开文件传输助手窗口...
[2025-07-28 23:03:39] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 23:03:39] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:03:40] [wxauto] [INFO] [Flask] [INFO] 成功创建wxauto.WeChat实例
[2025-07-28 23:03:40] [wxauto] [INFO] [Flask] [INFO] 微信实例初始化成功，但无法获取窗口名称，使用库: wxauto
[2025-07-28 23:03:40] [wxauto] [INFO] [Flask] [INFO] 正在打开文件传输助手窗口...
[2025-07-28 23:03:40] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 23:03:40] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:03:41] [Flask] [INFO] 文件传输助手窗口已打开
[2025-07-28 23:03:41] [Flask] [INFO] 微信连接监控已启动
[2025-07-28 23:03:41] [Flask] [INFO] 微信初始化成功，使用库: wxauto
[2025-07-28 23:03:41] [Flask] [DEBUG] 从nickname属性获取: Y
[2025-07-28 23:03:41] [Flask] [DEBUG] 成功获取窗口名称: Y
[2025-07-28 23:03:41] [Flask] [INFO] 初始化成功，获取到已登录窗口：Y
[2025-07-28 23:03:41] [Flask] [DEBUG] 微信初始化完成，文件传输助手窗口已在适配器中处理
[2025-07-28 23:03:41] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 2.43秒
[2025-07-28 23:03:41] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 2.22秒
[2025-07-28 23:03:41] [Flask] [DEBUG] 获取到窗口名称: Y
[2025-07-28 23:03:41] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 0.93秒
[2025-07-28 23:03:41] [Flask] [DEBUG] 获取会话列表成功，类型: <class 'NoneType'>
[2025-07-28 23:03:41] [Flask] [DEBUG] 获取会话列表返回None
[2025-07-28 23:03:41] [wxauto] [INFO] 微信自动初始化成功
[2025-07-28 23:03:41] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:03:41] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:03:41] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:03:41] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:03:41] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:03:41] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:03:41] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:03:41] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 文件传输助手窗口已打开
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 微信连接监控已启动
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 微信初始化成功，使用库: wxauto
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 从nickname属性获取: Y
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 成功获取窗口名称: Y
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 初始化成功，获取到已登录窗口：Y
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 微信初始化完成，文件传输助手窗口已在适配器中处理
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 2.43秒
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 2.22秒
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 获取到窗口名称: Y
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 0.93秒
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 获取会话列表成功，类型: <class 'NoneType'>
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 获取会话列表返回None
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:03:36] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 23:03:36)
[2025-07-28 23:03:42] [Flask] [DEBUG] 状态检查：获取到已登录窗口：Y
[2025-07-28 23:03:43] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 23:03:36)
[2025-07-28 23:03:43] [wxauto] [INFO] [Flask] [DEBUG] 状态检查：获取到已登录窗口：Y
[2025-07-28 23:03:43] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:03:43] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:03:43] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 1.66秒
[2025-07-28 23:03:44] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:03:44] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:03:44] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 1.66秒
[2025-07-28 23:03:39] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 3 次，最后: 2025-07-28 23:03:40)
[2025-07-28 23:03:47] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 3 次，最后: 2025-07-28 23:03:40)
[2025-07-28 23:03:41] [Flask] [INFO] 微信初始化成功，使用库: wxauto (重复 3 次，最后: 2025-07-28 23:03:41)
[2025-07-28 23:03:41] [Flask] [INFO] 初始化成功，获取到已登录窗口：Y (重复 3 次，最后: 2025-07-28 23:03:41)
[2025-07-28 23:03:41] [Flask] [DEBUG] 微信初始化完成，文件传输助手窗口已在适配器中处理 (重复 3 次，最后: 2025-07-28 23:03:41)
[2025-07-28 23:03:48] [wxauto] [INFO] [Flask] [INFO] 微信初始化成功，使用库: wxauto (重复 3 次，最后: 2025-07-28 23:03:41)
[2025-07-28 23:03:48] [wxauto] [INFO] [Flask] [INFO] 初始化成功，获取到已登录窗口：Y (重复 3 次，最后: 2025-07-28 23:03:41)
[2025-07-28 23:03:48] [wxauto] [INFO] [Flask] [DEBUG] 微信初始化完成，文件传输助手窗口已在适配器中处理 (重复 3 次，最后: 2025-07-28 23:03:41)
[2025-07-28 23:03:49] [Flask] [INFO] [wxautox] 自动登录请求参数: wxpath=None, timeout=10
[2025-07-28 23:03:49] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:49] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:49] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:49] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:49] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:49] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 23:03:49] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [INFO] [wxautox] 自动登录请求参数: wxpath=None, timeout=10
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 23:03:50] [wxauto] [INFO] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:51] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.06秒
[2025-07-28 23:03:51] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.24秒
[2025-07-28 23:03:52] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.06秒
[2025-07-28 23:03:52] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.24秒
[2025-07-28 23:03:39] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:41] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:54] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:03:54] [Flask] [ERROR] [wxautox] 自动登录失败: 'WeChatLoginWnd' object has no attribute 'control'
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:03:48)
[2025-07-28 23:03:55] [wxauto] [INFO] [Flask] [ERROR] [wxautox] 自动登录失败: 'WeChatLoginWnd' object has no attribute 'control'
[2025-07-28 23:03:56] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:03:56] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:03:56] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:03:56] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:03:56] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:03:56] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:03:56] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:03:56] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:03:43] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:03:51)
[2025-07-28 23:03:43] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:03:51)
[2025-07-28 23:03:58] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:03:51)
[2025-07-28 23:03:58] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:03:51)
[2025-07-28 23:04:00] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:04:00] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:04:00] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.34秒
[2025-07-28 23:04:00] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.34秒
[2025-07-28 23:04:00] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:04:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:03:40] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:03:55)
[2025-07-28 23:04:01] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:04:01] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:03:42] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:03:57)
[2025-07-28 23:04:04] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:04:05] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:04:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:04:05] [Flask] [ERROR] GetNextNewMessage调用失败: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
[2025-07-28 23:04:05] [Flask] [WARNING] GetNextNewMessage其他错误，返回空结果: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
[2025-07-28 23:04:05] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.77秒
[2025-07-28 23:03:44] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:04:00)
[2025-07-28 23:03:44] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:04:00)
[2025-07-28 23:04:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:04:06] [wxauto] [INFO] [Flask] [ERROR] GetNextNewMessage调用失败: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
[2025-07-28 23:04:06] [wxauto] [INFO] [Flask] [WARNING] GetNextNewMessage其他错误，返回空结果: (-2147220991, '事件无法调用任何订户', (None, None, None, 0, None))
[2025-07-28 23:04:06] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.77秒
[2025-07-28 23:03:56] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:03:56] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:00] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:10] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:04:10] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:04:10] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:04:10] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:04:10] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:04:10] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:04:10] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:04:10] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:04:10] [Flask] [ERROR] GetNextNewMessage调用失败: (-**********, '拒绝访问。', (None, None, None, 0, None))
[2025-07-28 23:04:10] [Flask] [WARNING] GetNextNewMessage其他错误，返回空结果: (-**********, '拒绝访问。', (None, None, None, 0, None))
[2025-07-28 23:04:10] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:04:04)
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [ERROR] GetNextNewMessage调用失败: (-**********, '拒绝访问。', (None, None, None, 0, None))
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [WARNING] GetNextNewMessage其他错误，返回空结果: (-**********, '拒绝访问。', (None, None, None, 0, None))
[2025-07-28 23:04:11] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:03:32] [wxauto] [INFO] API服务已停止 (重复 2 次，最后: 2025-07-28 23:04:11)
[2025-07-28 23:04:31] [wxauto] [INFO] 2025-07-28 23:04:31 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:43]  wxauto库检测成功 - 直接导入
[2025-07-28 23:04:31] [wxauto] [INFO] 2025-07-28 23:04:31 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:89]  打包环境中检测wxautox - 开始直接导入
[2025-07-28 23:04:31] [wxauto] [INFO] 2025-07-28 23:04:31 [app.wechat_lib_detector] [WARNING] [wechat_lib_detector.py:96]  sys.path中未找到wxautox相关路径
[2025-07-28 23:04:31] [wxauto] [INFO] 2025-07-28 23:04:31 [app.wechat_lib_detector] [INFO] [wechat_lib_detector.py:173]  打包环境中使用安全策略检测wxautox
[2025-07-28 23:04:32] [Flask] [INFO] ===== 开始启动API服务 =====
[2025-07-28 23:04:32] [Flask] [INFO] 正在检查互斥锁...
[2025-07-28 23:04:32] [Flask] [INFO] 成功从app包导入 app_mutex 模块
[2025-07-28 23:04:32] [Flask] [INFO] API服务端口: 5000
[2025-07-28 23:04:32] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000
[2025-07-28 23:04:32] [Flask] [INFO] 互斥锁检查通过
[2025-07-28 23:04:32] [Flask] [INFO] 正在检查依赖项...
[2025-07-28 23:04:32] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突
[2025-07-28 23:04:32] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化
[2025-07-28 23:04:32] [Flask] [INFO] 依赖项检查成功
[2025-07-28 23:04:32] [Flask] [INFO] 依赖项检查通过
[2025-07-28 23:04:32] [Flask] [INFO] 正在启动队列处理器...
[2025-07-28 23:04:32] [Flask] [INFO] 队列处理线程已启动
[2025-07-28 23:04:32] [Flask] [INFO] 已启动 5 个队列处理线程
[2025-07-28 23:04:32] [Flask] [INFO] 队列处理器已启动
[2025-07-28 23:04:32] [Flask] [INFO] 队列处理器启动成功
[2025-07-28 23:04:32] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0
[2025-07-28 23:04:32] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor']
[2025-07-28 23:04:32] [Flask] [INFO] 正在尝试导入Flask应用创建函数...
[2025-07-28 23:04:32] [Flask] [INFO] 成功从app包导入Flask应用创建函数
[2025-07-28 23:04:32] [Flask] [INFO] 正在创建Flask应用...
[2025-07-28 23:04:32] [Flask] [INFO] 成功创建Flask应用
[2025-07-28 23:04:32] [Flask] [INFO] 正在启动Flask应用...
[2025-07-28 23:04:32] [Flask] [INFO] 监听地址: 0.0.0.0:5000
[2025-07-28 23:04:32] [Flask] [INFO] 调试模式: True
[2025-07-28 23:04:33] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:04:33] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:04:33] [Flask] [DEBUG] 当前使用的库: None
[2025-07-28 23:04:33] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:04:33] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:04:33] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化
[2025-07-28 23:04:33] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:04:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:04:34] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 23:04:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:04:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:04:34] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:04:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:04:36] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:04:36] [Flask] [INFO] 尝试导入wxauto库
[2025-07-28 23:04:36] [Flask] [INFO] 打包环境中直接尝试导入wxauto库
[2025-07-28 23:04:36] [Flask] [INFO] 成功导入wxauto库（打包环境直接导入）
[2025-07-28 23:04:36] [Flask] [INFO] 使用微信自动化库: wxauto
[2025-07-28 23:04:36] [Flask] [ERROR] 微信初始化失败: 未找到微信窗口
[2025-07-28 23:04:36] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 500 - 耗时: 0.01秒
[2025-07-28 23:04:36] [wxauto] [INFO] 微信自动初始化失败: 初始化失败
[2025-07-28 23:04:36] [wxauto] [INFO] 将在 2 秒后重试...
[2025-07-28 23:03:35] [wxauto] [INFO] 启动命令: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe --service api --debug (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] 已设置控制台代码页为UTF-8 (65001) (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] API服务已启动，监听地址: 0.0.0.0:5000 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - Python版本: 3.11.13 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 13:03:15) [MSC v.1929 64 bit (AMD64)] (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor'] (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 是否在PyInstaller环境中运行: True (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 检测到打包环境，应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 已将应用根目录添加到Python路径: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 已将工作目录设置为应用根目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 修复后的工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 修复后的Python路径: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor'] (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 命令行参数: ['C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0\\wxauto_http_api.exe', '--service', 'api', '--debug'] (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 解析后的参数: service=api, debug=True, no_mutex_check=False (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 应用根目录设置完成: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 打包环境中跳过库检测，避免库冲突 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 微信自动化库将在实际使用时进行检测和初始化 (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] __main__ - INFO - 正在启动API服务... (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\comtypes\\gen\\__init__.py'> (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:35] [wxauto] [INFO] comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Local\Temp\comtypes_cache\wxauto_http_api-311' (重复 2 次，最后: 2025-07-28 23:04:31)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] ===== 开始启动API服务 ===== (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在检查互斥锁... (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入 app_mutex 模块 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] API服务端口: 5000 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功获取API服务互斥锁，端口: 5000 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 互斥锁检查通过 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在检查依赖项... (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 打包环境中跳过微信自动化库检测，避免库冲突 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 微信自动化库将在实际使用时进行检测和初始化 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 依赖项检查成功 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 依赖项检查通过 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在启动队列处理器... (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 已启动 5 个队列处理线程 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 队列处理器已启动 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 队列处理器启动成功 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 当前工作目录: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] Python路径: ['C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\app', 'C:\\Users\\<USER>\\Desktop\\Windows_x64_2.0.0.1\\Windows_x64_2.0.0', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\base_library.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\lib-dynload', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\win32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pythonwin', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\pywin32_system32', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\_MEI107802\\setuptools\\_vendor'] (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在尝试导入Flask应用创建函数... (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功从app包导入Flask应用创建函数 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在创建Flask应用... (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 成功创建Flask应用 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 正在启动Flask应用... (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 监听地址: 0.0.0.0:5000 (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] [Flask] [INFO] 调试模式: True (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] * Serving Flask app 'app' (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:03:36] [wxauto] [INFO] * Debug mode: on (重复 2 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:04:32] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:04:38] [wxauto] [INFO] 微信初始化尝试 2/3...
[2025-07-28 23:04:38] [Flask] [INFO] [wxautox] 获取二维码请求参数: wxpath=None
[2025-07-28 23:04:38] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:04:38] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:04:38] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:04:38] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:04:38] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:04:38] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 23:04:38] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:03:37] [wxauto] [INFO] 正在自动初始化微信... (重复 2 次，最后: 2025-07-28 23:04:33)
[2025-07-28 23:04:39] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:03:38] [wxauto] [INFO] 微信初始化尝试 1/3... (重复 2 次，最后: 2025-07-28 23:04:34)
[2025-07-28 23:04:42] [wxauto] [INFO] 微信初始化尝试 3/3...
[2025-07-28 23:04:36] [wxauto] [INFO] 将在 2 秒后重试... (重复 2 次，最后: 2025-07-28 23:04:40)
[2025-07-28 23:04:36] [wxauto] [INFO] 微信自动初始化失败: 初始化失败 (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:04:36] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:04:36] [Flask] [ERROR] 微信初始化失败: 未找到微信窗口 (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:04:36] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 500 - 耗时: 0.01秒 (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:04:33] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:04:33] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:04:33] [Flask] [DEBUG] 使用wxauto参数: {} (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:04:33] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:04:33] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化 (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:04:33] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒 (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:04:39] [Flask] [DEBUG] 当前使用的库: wxauto (重复 4 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:00] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:05:00] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:05:00] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:05:00] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:05:00] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:05:00] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化
[2025-07-28 23:05:00] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:04:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:05:04)
[2025-07-28 23:04:34] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:05:04)
[2025-07-28 23:04:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:05:04)
[2025-07-28 23:05:17] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: None
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/wechat/status
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 尝试导入wxauto库
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 打包环境中直接尝试导入wxauto库
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 成功导入wxauto库（打包环境直接导入）
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 使用微信自动化库: wxauto
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [ERROR] 微信初始化失败: 未找到微信窗口
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 500 - 耗时: 0.01秒
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 队列处理线程已启动 (重复 5 次，最后: 2025-07-28 23:04:32)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] [wxautox] 获取二维码请求参数: wxpath=None
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 从注册表找到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 找到有效的微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] [wxautox] 自动检测到微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] [wxautox] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 验证微信路径: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 微信文件大小: 660128 bytes
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 微信路径验证成功: d:\Program Files\Tencent\WeChat\WeChat.exe
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [ERROR] 微信初始化失败: 未找到微信窗口 (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 500 - 耗时: 0.01秒 (重复 3 次，最后: 2025-07-28 23:04:44)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {} (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化 (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒 (重复 5 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto (重复 4 次，最后: 2025-07-28 23:04:55)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:05:04)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:05:04)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:05:04)
[2025-07-28 23:05:17] [wxauto] [INFO] 初始化成功，获取到已登录窗口：Y
[2025-07-28 23:05:17] [Flask] [INFO] 成功创建wxauto.WeChat实例
[2025-07-28 23:05:17] [Flask] [INFO] 微信实例初始化成功，但无法获取窗口名称，使用库: wxauto
[2025-07-28 23:05:17] [Flask] [INFO] 正在打开文件传输助手窗口...
[2025-07-28 23:05:18] [wxauto] [INFO] [Flask] [INFO] 成功创建wxauto.WeChat实例
[2025-07-28 23:05:18] [wxauto] [INFO] [Flask] [INFO] 微信实例初始化成功，但无法获取窗口名称，使用库: wxauto
[2025-07-28 23:05:18] [wxauto] [INFO] [Flask] [INFO] 正在打开文件传输助手窗口...
[2025-07-28 23:05:19] [Flask] [INFO] 文件传输助手窗口已打开
[2025-07-28 23:05:19] [Flask] [INFO] 微信连接监控已启动
[2025-07-28 23:05:19] [Flask] [DEBUG] 获取到窗口名称: Y
[2025-07-28 23:05:19] [Flask] [INFO] 微信初始化成功，使用库: wxauto
[2025-07-28 23:05:19] [Flask] [DEBUG] 获取会话列表成功，类型: <class 'NoneType'>
[2025-07-28 23:05:19] [Flask] [DEBUG] 从nickname属性获取: Y
[2025-07-28 23:05:19] [Flask] [DEBUG] 获取会话列表返回None
[2025-07-28 23:05:19] [Flask] [DEBUG] 成功获取窗口名称: Y
[2025-07-28 23:05:19] [Flask] [INFO] 初始化成功，获取到已登录窗口：Y
[2025-07-28 23:05:19] [Flask] [DEBUG] 微信初始化完成，文件传输助手窗口已在适配器中处理
[2025-07-28 23:05:19] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 2.43秒
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [INFO] 文件传输助手窗口已打开
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [INFO] 微信连接监控已启动
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [DEBUG] 获取到窗口名称: Y
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [INFO] 微信初始化成功，使用库: wxauto
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [DEBUG] 获取会话列表成功，类型: <class 'NoneType'>
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [DEBUG] 从nickname属性获取: Y
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [DEBUG] 获取会话列表返回None
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [DEBUG] 成功获取窗口名称: Y
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [INFO] 初始化成功，获取到已登录窗口：Y
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [DEBUG] 微信初始化完成，文件传输助手窗口已在适配器中处理
[2025-07-28 23:05:20] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: POST /api/wechat/initialize - 状态码: 200 - 耗时: 2.43秒
[2025-07-28 23:05:20] [Flask] [DEBUG] 状态检查：获取到已登录窗口：Y
[2025-07-28 23:05:20] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:05:20] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:20] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:05:20] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:05:20] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:05:00] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化 (重复 4 次，最后: 2025-07-28 23:05:16)
[2025-07-28 23:05:00] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:05:16)
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [DEBUG] 状态检查：获取到已登录窗口：Y
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化 (重复 4 次，最后: 2025-07-28 23:05:16)
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:05:16)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [ERROR] 获取新消息失败: 微信实例未初始化 (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: POST /api/wechat/initialize (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:05:17)
[2025-07-28 23:05:24] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:05:24] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:05:24] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.39秒
[2025-07-28 23:05:25] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:05:25] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:05:25] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.39秒
[2025-07-28 23:05:00] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:00] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:00] [Flask] [DEBUG] 当前使用的库: wxauto (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:00] [Flask] [DEBUG] 使用wxauto参数: {} (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:00] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:26] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:26] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:26] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:26] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {} (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:26] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 5 次，最后: 2025-07-28 23:05:20)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:05:21)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:05:21)
[2025-07-28 23:05:28] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:05:28] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:05:28] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:05:28] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:05:28] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:05:29] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:05:29] [wxauto] [INFO] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:05:29] [wxauto] [INFO] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:05:29] [wxauto] [INFO] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:05:29] [wxauto] [INFO] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:05:32] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.37秒
[2025-07-28 23:05:33] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.37秒
[2025-07-28 23:05:20] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:05:28)
[2025-07-28 23:05:20] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:05:28)
[2025-07-28 23:05:20] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:05:28)
[2025-07-28 23:05:34] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:05:28)
[2025-07-28 23:05:34] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:05:28)
[2025-07-28 23:05:34] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:05:28)
[2025-07-28 23:05:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:05:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:05:36] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:05:36] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:05:36] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:05:24] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:05:32)
[2025-07-28 23:05:24] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:05:32)
[2025-07-28 23:05:38] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:05:32)
[2025-07-28 23:05:38] [wxauto] [INFO] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:05:32)
[2025-07-28 23:05:40] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:05:40] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:05:40] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.25秒
[2025-07-28 23:05:20] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:05:34)
[2025-07-28 23:05:20] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:05:34)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:05:35)
[2025-07-28 23:05:17] [wxauto] [INFO] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:05:36)
[2025-07-28 23:05:28] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:05:36)
[2025-07-28 23:05:28] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:05:36)
[2025-07-28 23:05:28] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:05:36)
[2025-07-28 23:05:28] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:05:36)
[2025-07-28 23:05:28] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:05:36)
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:05:37)
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:05:37)
[2025-07-28 23:05:21] [wxauto] [INFO] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:05:37)
[2025-07-28 23:05:44] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:05:44] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:05:44] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:05:44] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:05:44] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:05:47] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.06秒
[2025-07-28 23:05:47] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.10秒
[2025-07-28 23:04:34] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 27 次，最后: 2025-07-28 23:05:44)
[2025-07-28 23:05:36] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:05:44)
[2025-07-28 23:05:36] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:05:44)
[2025-07-28 23:05:36] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:05:44)
[2025-07-28 23:05:50] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:51] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:05:51] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:05:52] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:05:52] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:05:52] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:05:40] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:05:47)
[2025-07-28 23:05:40] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:05:47)
[2025-07-28 23:05:56] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:05:56] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:05:44] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:05:52)
[2025-07-28 23:05:44] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:05:52)
[2025-07-28 23:05:44] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:05:52)
[2025-07-28 23:05:44] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:05:52)
[2025-07-28 23:05:44] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:05:52)
[2025-07-28 23:05:40] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.25秒 (重复 2 次，最后: 2025-07-28 23:05:56)
[2025-07-28 23:05:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:06:04)
[2025-07-28 23:05:51] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:06:04)
[2025-07-28 23:05:51] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:06:04)
[2025-07-28 23:05:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:06:05)
[2025-07-28 23:06:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:06:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:06:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:06:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:06:34] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:07:04)
[2025-07-28 23:06:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:07:04)
[2025-07-28 23:06:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:07:04)
[2025-07-28 23:06:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:07:05)
[2025-07-28 23:07:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:07:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:07:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:07:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:07:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:08:04)
[2025-07-28 23:07:34] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:08:04)
[2025-07-28 23:07:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:08:04)
[2025-07-28 23:07:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:08:05)
[2025-07-28 23:08:28] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:08:28] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:08:28] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:08:28] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:08:28] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:08:28] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:08:28] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:08:28] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:08:28] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:08:28] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:08:32] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:08:32] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:08:32] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.13秒
[2025-07-28 23:08:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:08:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:08:40] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.20秒
[2025-07-28 23:08:28] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:08:34)
[2025-07-28 23:08:28] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:08:34)
[2025-07-28 23:08:28] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:28] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:08:36)
[2025-07-28 23:08:44] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:08:44] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:08:44] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:08:44] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:08:44] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:08:44] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:08:44] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:08:44] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:08:32] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:08:40)
[2025-07-28 23:08:32] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:08:40)
[2025-07-28 23:08:46] [Flask] [ERROR] GetNextNewMessage调用失败: list index out of range
[2025-07-28 23:08:46] [Flask] [WARNING] GetNextNewMessage其他错误，返回空结果: list index out of range
[2025-07-28 23:08:46] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 1.34秒
[2025-07-28 23:08:54] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:08:54] [Flask] [INFO] === wxauto返回结果内容: {'chat_name': 'Jooo', 'chat_type': 'friend', 'msg': [<wxauto - FriendTextMessage(111) at 0x2adcfa7a690>]} ===
[2025-07-28 23:08:54] [Flask] [DEBUG] wxauto原始返回结果: {'chat_name': 'Jooo', 'chat_type': 'friend', 'msg': [<wxauto - FriendTextMessage(111) at 0x2adcfa7a690>]}
[2025-07-28 23:08:54] [Flask] [DEBUG] wxauto返回结果类型: <class 'dict'>
[2025-07-28 23:08:54] [Flask] [DEBUG] result是字典，键: ['chat_name', 'chat_type', 'msg']
[2025-07-28 23:08:54] [Flask] [DEBUG]   chat_name: type=<class 'str'>, value=Jooo
[2025-07-28 23:08:54] [Flask] [DEBUG]   chat_type: type=<class 'str'>, value=friend
[2025-07-28 23:08:54] [Flask] [DEBUG]   msg: type=<class 'list'>, value=[<wxauto - FriendTextMessage(111) at 0x2adcfa7a690>]
[2025-07-28 23:08:54] [Flask] [DEBUG] 处理字典格式的result
[2025-07-28 23:08:54] [Flask] [DEBUG] 字典键: ['chat_name', 'chat_type', 'msg']
[2025-07-28 23:08:54] [Flask] [DEBUG] 检测到wxauto字典格式，保持字典结构并转换消息对象
[2025-07-28 23:08:54] [Flask] [DEBUG] wxauto字典格式转换完成，保持chat_name: Jooo
[2025-07-28 23:08:54] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.18秒
[2025-07-28 23:08:44] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:44] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:08:51)
[2025-07-28 23:08:59] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:08:59] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:08:59] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:08:59] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:08:59] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:08:59] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:08:59] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:08:59] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:08:59] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:08:59] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:09:02] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:09:02] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.27秒
[2025-07-28 23:09:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:08:54] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:09:02)
[2025-07-28 23:08:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:09:04)
[2025-07-28 23:08:59] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:09:04)
[2025-07-28 23:08:59] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:09:04)
[2025-07-28 23:09:10] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:09:10] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.37秒
[2025-07-28 23:08:59] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:08:59] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:09:07)
[2025-07-28 23:09:15] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:09:15] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:09:15] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:09:15] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:09:15] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:09:15] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:09:15] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:09:15] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:09:02] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:09:10)
[2025-07-28 23:09:18] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:09:10] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:09:18)
[2025-07-28 23:09:10] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.37秒 (重复 2 次，最后: 2025-07-28 23:09:18)
[2025-07-28 23:09:25] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.11秒
[2025-07-28 23:09:26] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:09:26] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.34秒
[2025-07-28 23:05:50] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 78 次，最后: 2025-07-28 23:09:22)
[2025-07-28 23:09:28] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:09:15] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:15] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:09:23)
[2025-07-28 23:09:29] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:09:29] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:09:31] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:09:31] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:09:31] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:09:31] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:09:31] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:09:31] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:09:31] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:09:31] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:09:18] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:09:26)
[2025-07-28 23:09:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:09:35] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:09:35] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.74秒
[2025-07-28 23:09:40] [Flask] [INFO] === wxauto返回结果内容: {'chat_name': 'Jooo', 'chat_type': 'friend', 'msg': [<wxauto - SystemMessage(以下为新消息) at 0x2adcfa58690>, <wxauto - FriendTextMessage(在吗) at 0x2adcfa92090>]} ===
[2025-07-28 23:09:40] [Flask] [DEBUG] wxauto原始返回结果: {'chat_name': 'Jooo', 'chat_type': 'friend', 'msg': [<wxauto - SystemMessage(以下为新消息) at 0x2adcfa58690>, <wxauto - FriendTextMessage(在吗) at 0x2adcfa92090>]}
[2025-07-28 23:09:40] [Flask] [DEBUG]   msg: type=<class 'list'>, value=[<wxauto - SystemMessage(以下为新消息) at 0x2adcfa58690>, <wxauto - FriendTextMessage(在吗) at 0x2adcfa92090>]
[2025-07-28 23:09:40] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 0.68秒
[2025-07-28 23:09:29] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:09:34)
[2025-07-28 23:09:29] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:09:34)
[2025-07-28 23:08:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:09:35)
[2025-07-28 23:09:42] [Flask] [INFO] 收到请求: POST /api/message/listen/add
[2025-07-28 23:09:42] [Flask] [DEBUG] 请求体: {'nickname': 'Jooo'}
[2025-07-28 23:09:42] [Flask] [INFO] 添加监听对象: Jooo, 使用库: wxauto
[2025-07-28 23:09:42] [Flask] [INFO] wx_instance类型: <class 'app.wechat_adapter.WeChatAdapter'>
[2025-07-28 23:09:42] [Flask] [INFO] original_instance类型: <class 'wxauto.wx.WeChat'>
[2025-07-28 23:09:42] [Flask] [INFO] 是否有_instance属性: True
[2025-07-28 23:09:42] [Flask] [INFO] original_instance是否等于wx_instance: False
[2025-07-28 23:08:54] [Flask] [DEBUG] wxauto返回结果类型: <class 'dict'> (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG] result是字典，键: ['chat_name', 'chat_type', 'msg'] (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG]   chat_name: type=<class 'str'>, value=Jooo (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG]   chat_type: type=<class 'str'>, value=friend (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG] 处理字典格式的result (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG] 字典键: ['chat_name', 'chat_type', 'msg'] (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG] 检测到wxauto字典格式，保持字典结构并转换消息对象 (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:08:54] [Flask] [DEBUG] wxauto字典格式转换完成，保持chat_name: Jooo (重复 2 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:09:26] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 3 次，最后: 2025-07-28 23:09:40)
[2025-07-28 23:09:31] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:31] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:09:39)
[2025-07-28 23:09:45] [Flask] [DEBUG] AddListenChat返回值类型: <class 'wxauto.wx.Chat'>, 值: <wxauto - Chat object("Jooo")>
[2025-07-28 23:09:45] [Flask] [DEBUG] listen[Jooo]的类型: <class 'tuple'>, 值: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>)
[2025-07-28 23:09:45] [Flask] [INFO] 成功添加监听对象: Jooo
[2025-07-28 23:09:45] [Flask] [INFO] 请求处理完成: POST /api/message/listen/add - 状态码: 200 - 耗时: 3.00秒
[2025-07-28 23:09:47] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:09:47] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:09:47] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:09:47] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:09:50] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:09:50] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:09:50] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:09:50] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:09:50] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:09:50] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:09:50] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:09:50] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:09:54] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:09:54] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.60秒
[2025-07-28 23:09:35] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:09:54)
[2025-07-28 23:09:47] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:09:54)
[2025-07-28 23:09:47] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:09:54)
[2025-07-28 23:09:47] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:09:54)
[2025-07-28 23:09:47] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:09:54)
[2025-07-28 23:10:00] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:10:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:01] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:10:01] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:10:01] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:10:01] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:09:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:10:04)
[2025-07-28 23:10:00] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:10:04)
[2025-07-28 23:10:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:10:04)
[2025-07-28 23:10:01] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:10:08)
[2025-07-28 23:10:01] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:10:08)
[2025-07-28 23:10:01] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:10:08)
[2025-07-28 23:10:01] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:10:08)
[2025-07-28 23:10:15] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:10:15] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:10:15] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:10:15] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:26] [Flask] [DEBUG] AddListenChat返回值类型: <class 'wxauto.param.WxResponse'>, 值: {'status': '失败', 'message': '该聊天已监听', 'data': None}
[2025-07-28 23:10:26] [Flask] [INFO] 请求处理完成: POST /api/message/listen/add - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:29] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:10:29] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:09:42] [Flask] [INFO] 收到请求: POST /api/message/listen/add (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:42] [Flask] [DEBUG] 请求体: {'nickname': 'Jooo'} (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:42] [Flask] [INFO] 添加监听对象: Jooo, 使用库: wxauto (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:42] [Flask] [INFO] wx_instance类型: <class 'app.wechat_adapter.WeChatAdapter'> (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:42] [Flask] [INFO] original_instance类型: <class 'wxauto.wx.WeChat'> (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:42] [Flask] [INFO] 是否有_instance属性: True (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:42] [Flask] [INFO] original_instance是否等于wx_instance: False (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:45] [Flask] [DEBUG] listen[Jooo]的类型: <class 'tuple'>, 值: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>) (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:09:45] [Flask] [INFO] 成功添加监听对象: Jooo (重复 2 次，最后: 2025-07-28 23:10:26)
[2025-07-28 23:10:15] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:10:28)
[2025-07-28 23:10:15] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:10:28)
[2025-07-28 23:10:15] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:10:28)
[2025-07-28 23:10:15] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:10:28)
[2025-07-28 23:10:35] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:10:35] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:10:35] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:10:35] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:36] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:10:37] [Flask] [INFO] wxauto收到消息: <wxauto - SystemMessage(以下为新消息) at 0x2adcfa85450>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:10:37] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'base', 'content': '以下为新消息', 'sender': 'system', 'id': '4245904844721', 'mtype': None, 'sender_remark': 'system', 'file_path': None, 'time': None}
[2025-07-28 23:10:37] [Flask] [INFO] wxauto收到消息: <wxauto - FriendTextMessage(111) at 0x2adcfa87cd0>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:10:37] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'text', 'content': '111', 'sender': 'Jooo', 'id': '4245904844719', 'mtype': None, 'sender_remark': 'Jooo', 'file_path': None, 'time': None}
[2025-07-28 23:10:29] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:10:36)
[2025-07-28 23:10:29] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:10:36)
[2025-07-28 23:10:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:10:37)
[2025-07-28 23:10:42] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:10:42] [Flask] [INFO] 返回 Jooo 的 2 条消息
[2025-07-28 23:10:35] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:10:42)
[2025-07-28 23:10:35] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:10:42)
[2025-07-28 23:10:35] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:10:42)
[2025-07-28 23:10:52] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:10:52] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.38秒
[2025-07-28 23:09:50] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:09:50] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:10:48)
[2025-07-28 23:10:56] [Flask] [INFO] 收到请求: POST /api/chat-window/message/send
[2025-07-28 23:10:56] [Flask] [DEBUG] 请求体: {'who': 'Jooo', 'message': '您好！我是您的公寓管家客服，很高兴为您服务。  \n\n1. **WiFi信息**：  \n   - **名称**：以`LV`开头（例如`LV-大厅`）。  \n   - **密码**：`88888888`（8个八）。  \n\n2. **床品说明**：  \n   - 您的床单、被套和枕套已清洗消毒，并晾晒在阳台，您可以随时取用。  \n\n3. **视频导览**：  \n   - 为了帮助您更好地使用房间设施，请先观看我们的**房间设施视频导览**，里面有详细的操作说明。  \n\n如果您还有其他问题，欢迎随时咨询！如果多次提问或需要更详细的解答，也可以联系群里的管家哦~ 😊', 'clear': True, 'at_list': ['Jooo']}
[2025-07-28 23:10:56] [Flask] [DEBUG] 发送消息，当前使用的库: wxauto
[2025-07-28 23:10:56] [Flask] [DEBUG] chat_wnd类型: <class 'tuple'>, 值: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>)
[2025-07-28 23:10:56] [Flask] [ERROR] 检测到chat_wnd是tuple类型: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>)
[2025-07-28 23:10:56] [Flask] [INFO] 使用tuple的第一个元素: <class 'wxauto.wx.Chat'>
[2025-07-28 23:09:54] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:10:52)
[2025-07-28 23:10:59] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:10:59] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:11:00] [Flask] [INFO] 请求处理完成: POST /api/chat-window/message/send - 状态码: 200 - 耗时: 4.17秒
[2025-07-28 23:11:02] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:11:02] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:11:02] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:11:02] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:11:02] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:11:02] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:11:02] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:11:02] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:11:02] [Flask] [INFO] wxauto收到消息: <wxauto - SelfTextMessage(您好！我是您的公...) at 0x2adcfb5f850>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:11:02] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'text', 'content': '您好！我是您的公寓管家客服，很高兴为您服务。  \n\n1. **WiFi信息**：  \n   - **名称**：以`LV`开头（例如`LV-大厅`）。  \n   - **密码**：`88888888`（8个八）。  \n\n2. **床品说明**：  \n   - 您的床单、被套和枕套已清洗消毒，并晾晒在阳台，您可以随时取用。  \n\n3. **视频导览**：  \n   - 为了帮助您更好地使用房间设施，请先观看我们的**房间设施视频导览**，里面有详细的操作说明。  \n\n如果您还有其他问题，欢迎随时咨询！如果多次提问或需要更详细的解答，也可以联系群里的管家哦~ 😊', 'sender': 'self', 'id': '4245904844998', 'mtype': None, 'sender_remark': 'self', 'file_path': None, 'time': None}
[2025-07-28 23:11:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:11:06] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:11:06] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.25秒
[2025-07-28 23:11:08] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:11:08] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:11:08] [Flask] [INFO] 返回 Jooo 的 1 条消息
[2025-07-28 23:11:08] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:36] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:11:04)
[2025-07-28 23:10:59] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:11:04)
[2025-07-28 23:10:59] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:11:04)
[2025-07-28 23:10:52] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:11:06)
[2025-07-28 23:11:13] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:11:13] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.56秒
[2025-07-28 23:10:42] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:11:08)
[2025-07-28 23:11:02] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:02] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:11:10)
[2025-07-28 23:11:16] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:11:18] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:11:18] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:11:18] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:11:18] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:11:18] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:11:18] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:11:18] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:11:18] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:11:19] [Flask] [ERROR] GetNextNewMessage调用失败: I/O operation on closed file.
[2025-07-28 23:11:19] [Flask] [WARNING] GetNextNewMessage其他错误，返回空结果: I/O operation on closed file.
[2025-07-28 23:11:19] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 1.16秒
[2025-07-28 23:11:06] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:11:13)
[2025-07-28 23:11:19] [Flask] [INFO] wxauto收到消息: <wxauto - SystemMessage(以下为新消息) at 0x2adce8d27d0>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:11:19] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'base', 'content': '以下为新消息', 'sender': 'system', 'id': '42459048441013', 'mtype': None, 'sender_remark': 'system', 'file_path': None, 'time': None}
[2025-07-28 23:11:19] [Flask] [INFO] wxauto收到消息: <wxauto - FriendTextMessage(6666) at 0x2adcfa92990>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:11:19] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'text', 'content': '6666', 'sender': 'Jooo', 'id': '42459048441011', 'mtype': None, 'sender_remark': 'Jooo', 'file_path': None, 'time': None}
[2025-07-28 23:10:35] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:11:16)
[2025-07-28 23:11:08] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:11:16)
[2025-07-28 23:11:08] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:11:16)
[2025-07-28 23:11:08] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:11:16)
[2025-07-28 23:11:23] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:11:23] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:11:23] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:10:42] [Flask] [INFO] 返回 Jooo 的 2 条消息 (重复 2 次，最后: 2025-07-28 23:11:23)
[2025-07-28 23:11:16] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:11:23)
[2025-07-28 23:11:28] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:11:28] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.35秒
[2025-07-28 23:11:29] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:11:29] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:11:18] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:18] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:11:24)
[2025-07-28 23:11:32] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:11:32] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:11:32] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:11:32] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:11:32] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:11:32] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:11:32] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:11:32] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:11:13] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:11:28)
[2025-07-28 23:11:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:11:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:11:36] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:11:36] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.45秒
[2025-07-28 23:11:36] [Flask] [DEBUG] 请求体: {'who': 'Jooo', 'message': '您好！很高兴为您服务，我是公寓管家客服。请问您需要了解什么信息呢？  \n\n1. **WiFi信息**：  \n   - **名称**：以`LV`开头（如`LV-大厅`）。  \n   - **密码**：`88888888`（8个八）。  \n\n2. **床品说明**：  \n   - 您的床单、被套和枕套已清洗消毒，现在晾在阳台，您可以随时取用。  \n\n3. **视频导览**：  \n   - 为了帮助您更好地使用房间设施，请先观看房间设施视频，里面有详细的操作指引。  \n\n如果您还有其他问题，欢迎随时咨询！如果问题较多或需要更详细的解答，也可以联系群里的管家哦~ 😊', 'clear': True, 'at_list': ['Jooo']}
[2025-07-28 23:11:39] [Flask] [INFO] 请求处理完成: POST /api/chat-window/message/send - 状态码: 200 - 耗时: 2.84秒
[2025-07-28 23:11:29] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:11:34)
[2025-07-28 23:11:29] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:11:34)
[2025-07-28 23:11:40] [Flask] [INFO] wxauto收到消息: <wxauto - SelfTextMessage(您好！很高兴为您...) at 0x2adcfb43e50>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:11:40] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'text', 'content': '您好！很高兴为您服务，我是公寓管家客服。请问您需要了解什么信息呢？  \n\n1. **WiFi信息**：  \n   - **名称**：以`LV`开头（如`LV-大厅`）。  \n   - **密码**：`88888888`（8个八）。  \n\n2. **床品说明**：  \n   - 您的床单、被套和枕套已清洗消毒，现在晾在阳台，您可以随时取用。  \n\n3. **视频导览**：  \n   - 为了帮助您更好地使用房间设施，请先观看房间设施视频，里面有详细的操作指引。  \n\n如果您还有其他问题，欢迎随时咨询！如果问题较多或需要更详细的解答，也可以联系群里的管家哦~ 😊', 'sender': 'self', 'id': '42459048441034', 'mtype': None, 'sender_remark': 'self', 'file_path': None, 'time': None}
[2025-07-28 23:11:28] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:11:36)
[2025-07-28 23:10:56] [Flask] [INFO] 收到请求: POST /api/chat-window/message/send (重复 2 次，最后: 2025-07-28 23:11:36)
[2025-07-28 23:10:56] [Flask] [DEBUG] 发送消息，当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:11:36)
[2025-07-28 23:10:56] [Flask] [DEBUG] chat_wnd类型: <class 'tuple'>, 值: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>) (重复 2 次，最后: 2025-07-28 23:11:36)
[2025-07-28 23:10:56] [Flask] [ERROR] 检测到chat_wnd是tuple类型: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>) (重复 2 次，最后: 2025-07-28 23:11:36)
[2025-07-28 23:10:56] [Flask] [INFO] 使用tuple的第一个元素: <class 'wxauto.wx.Chat'> (重复 2 次，最后: 2025-07-28 23:11:36)
[2025-07-28 23:11:44] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:11:44] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.38秒
[2025-07-28 23:11:32] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:32] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:11:41)
[2025-07-28 23:11:47] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:11:49] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:11:49] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:11:49] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:11:49] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:11:49] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:11:49] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:11:49] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:11:49] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:11:36] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:11:44)
[2025-07-28 23:11:52] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:11:52] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.66秒
[2025-07-28 23:11:08] [Flask] [INFO] 返回 Jooo 的 1 条消息 (重复 2 次，最后: 2025-07-28 23:11:47)
[2025-07-28 23:11:23] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:11:47)
[2025-07-28 23:11:23] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:11:47)
[2025-07-28 23:11:23] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:11:47)
[2025-07-28 23:11:54] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:11:54] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:11:54] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:11:54] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:11:44] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:11:52)
[2025-07-28 23:11:59] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:11:59] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:11:47] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:11:54)
[2025-07-28 23:12:00] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:12:01] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:11:49] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:11:49] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:11:57)
[2025-07-28 23:12:05] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:12:05] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:12:05] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:12:05] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:12:05] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:12:05] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:12:05] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:12:05] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:12:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:11:13] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.56秒 (重复 2 次，最后: 2025-07-28 23:12:00)
[2025-07-28 23:11:52] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:12:00)
[2025-07-28 23:11:54] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:12:01)
[2025-07-28 23:11:54] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:12:01)
[2025-07-28 23:11:54] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:12:01)
[2025-07-28 23:11:54] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:12:01)
[2025-07-28 23:12:08] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:12:08] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:12:08] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:12:08] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:12:08] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:12:08] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.91秒
[2025-07-28 23:11:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:12:04)
[2025-07-28 23:11:59] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:12:04)
[2025-07-28 23:11:59] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:12:04)
[2025-07-28 23:12:00] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:12:08)
[2025-07-28 23:12:01] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:12:08)
[2025-07-28 23:12:15] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:12:08] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:12:15)
[2025-07-28 23:12:08] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:12:15)
[2025-07-28 23:12:08] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:12:15)
[2025-07-28 23:12:08] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:12:15)
[2025-07-28 23:12:23] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:12:23] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:12:23] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:12:23] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:12:15] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:12:23)
[2025-07-28 23:12:30] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:12:30] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:12:30] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:12:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:12:23] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:12:30)
[2025-07-28 23:12:23] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:12:30)
[2025-07-28 23:12:23] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:12:30)
[2025-07-28 23:12:23] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:12:30)
[2025-07-28 23:12:37] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:12:37] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:12:37] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:12:37] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:12:30] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:12:34)
[2025-07-28 23:12:30] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:12:34)
[2025-07-28 23:12:41] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:12:41] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.86秒
[2025-07-28 23:12:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:12:35)
[2025-07-28 23:12:05] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:05] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:30] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:12:37)
[2025-07-28 23:12:44] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:12:45] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:12:45] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:12:45] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:12:45] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:12:45] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:12:45] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:12:45] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:12:45] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:12:08] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:12:41)
[2025-07-28 23:12:48] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:12:48] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.50秒
[2025-07-28 23:12:37] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:12:44)
[2025-07-28 23:12:37] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:12:44)
[2025-07-28 23:12:37] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:12:44)
[2025-07-28 23:12:37] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:12:44)
[2025-07-28 23:12:51] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:12:51] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:12:51] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:12:51] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:12:41] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:12:48)
[2025-07-28 23:12:44] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:12:51)
[2025-07-28 23:12:57] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:12:57] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.88秒
[2025-07-28 23:12:58] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:12:45] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:12:45] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:12:53)
[2025-07-28 23:13:00] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:13:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:13:01] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:13:01] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:13:01] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:13:01] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:13:01] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:13:01] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:13:01] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:13:01] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:12:48] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:12:57)
[2025-07-28 23:12:51] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:12:58)
[2025-07-28 23:12:51] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:12:58)
[2025-07-28 23:12:51] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:12:58)
[2025-07-28 23:12:51] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:12:58)
[2025-07-28 23:13:04] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:13:04] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.32秒
[2025-07-28 23:13:05] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:13:05] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:13:05] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:13:05] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:13:06] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:12:57] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:13:04)
[2025-07-28 23:12:58] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:13:05)
[2025-07-28 23:12:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:13:05)
[2025-07-28 23:13:00] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:13:05)
[2025-07-28 23:13:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:13:05)
[2025-07-28 23:13:12] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:13:05] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:13:12)
[2025-07-28 23:13:05] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:13:12)
[2025-07-28 23:13:05] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:13:12)
[2025-07-28 23:13:05] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:13:12)
[2025-07-28 23:13:19] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:13:19] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:13:19] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:13:19] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:13:12] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:13:19)
[2025-07-28 23:13:26] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:13:30] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:13:30] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:13:19] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:13:26)
[2025-07-28 23:13:19] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:13:26)
[2025-07-28 23:13:19] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:13:26)
[2025-07-28 23:13:19] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:13:26)
[2025-07-28 23:13:33] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:13:33] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:13:33] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:13:33] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:13:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:13:26] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:13:33)
[2025-07-28 23:13:30] [Flask] [INFO] 收到请求: GET /api/health (重复 2 次，最后: 2025-07-28 23:13:34)
[2025-07-28 23:13:30] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:13:34)
[2025-07-28 23:13:40] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:13:06] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:13:35)
[2025-07-28 23:13:33] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:13:40)
[2025-07-28 23:13:33] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:13:40)
[2025-07-28 23:13:33] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:13:40)
[2025-07-28 23:13:33] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:13:40)
[2025-07-28 23:13:47] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:13:47] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:13:47] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:13:47] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:13:40] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:13:47)
[2025-07-28 23:13:54] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:13:58] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:13:58] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.88秒
[2025-07-28 23:13:01] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:01] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:47] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:47] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:47] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:13:47] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:13:54)
[2025-07-28 23:14:00] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:14:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:01] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:14:01] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:14:01] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:14:01] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:02] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:14:02] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:14:02] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:14:02] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:14:02] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:14:02] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:14:02] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:14:02] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:13:04] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:13:58)
[2025-07-28 23:14:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:14:06] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.08秒
[2025-07-28 23:14:06] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:14:06] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.81秒
[2025-07-28 23:13:54] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:14:01)
[2025-07-28 23:14:08] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:13:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:14:04)
[2025-07-28 23:14:00] [Flask] [INFO] 收到请求: GET /api/health (重复 3 次，最后: 2025-07-28 23:14:04)
[2025-07-28 23:14:00] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 3 次，最后: 2025-07-28 23:14:04)
[2025-07-28 23:13:58] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:14:06)
[2025-07-28 23:14:14] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:14:14] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.54秒
[2025-07-28 23:14:01] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:14:08)
[2025-07-28 23:14:01] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:14:08)
[2025-07-28 23:14:01] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:14:08)
[2025-07-28 23:14:01] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:14:08)
[2025-07-28 23:14:15] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:14:15] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:14:15] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:14:15] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:02] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:02] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:14:10)
[2025-07-28 23:14:18] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:14:18] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:14:18] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:14:18] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:14:18] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:14:18] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:14:18] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:14:18] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:14:06] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:14:14)
[2025-07-28 23:14:08] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:14:15)
[2025-07-28 23:14:22] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:14:22] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:14:22] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.92秒
[2025-07-28 23:14:14] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:14:22)
[2025-07-28 23:14:15] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:14:22)
[2025-07-28 23:14:15] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:14:22)
[2025-07-28 23:14:15] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:14:22)
[2025-07-28 23:14:15] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:14:22)
[2025-07-28 23:14:29] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:14:29] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:14:29] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:14:29] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:32] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:14:32] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:14:22] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:14:29)
[2025-07-28 23:14:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:14:36] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:14:29] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:14:36)
[2025-07-28 23:14:29] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:14:36)
[2025-07-28 23:14:29] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:14:36)
[2025-07-28 23:14:29] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:14:36)
[2025-07-28 23:14:43] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:14:43] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:14:43] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:14:43] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:48] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:14:48] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.79秒
[2025-07-28 23:14:18] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:18] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:14:44)
[2025-07-28 23:14:36] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:14:43)
[2025-07-28 23:14:50] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:14:52] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:14:52] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:14:52] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:14:52] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:14:52] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:14:52] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:14:52] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:14:52] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:14:22] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:14:48)
[2025-07-28 23:14:43] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:14:50)
[2025-07-28 23:14:43] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:14:50)
[2025-07-28 23:14:43] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:14:50)
[2025-07-28 23:14:43] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:14:50)
[2025-07-28 23:14:56] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:14:56] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.68秒
[2025-07-28 23:14:57] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:14:57] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:14:57] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:14:57] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:14:48] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:14:56)
[2025-07-28 23:14:50] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:14:57)
[2025-07-28 23:15:04] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:15:04] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.30秒
[2025-07-28 23:15:04] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:14:52] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:14:52] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:15:00)
[2025-07-28 23:15:08] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:15:08] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:15:08] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:15:08] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:15:08] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:15:08] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:15:08] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:15:08] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:14:56] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:15:04)
[2025-07-28 23:14:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:15:05)
[2025-07-28 23:14:57] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:15:04)
[2025-07-28 23:14:57] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:15:04)
[2025-07-28 23:14:57] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:15:04)
[2025-07-28 23:14:57] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:15:04)
[2025-07-28 23:14:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:15:06)
[2025-07-28 23:15:11] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:15:11] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:15:11] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:15:11] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:15:12] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:15:04] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:15:11)
[2025-07-28 23:14:56] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.68秒 (重复 2 次，最后: 2025-07-28 23:15:12)
[2025-07-28 23:15:04] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:15:12)
[2025-07-28 23:15:19] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:15:19] [Flask] [INFO] wxauto收到消息: <wxauto - SystemMessage(以下为新消息) at 0x2adcfac3c50>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:15:19] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'base', 'content': '以下为新消息', 'sender': 'system', 'id': '42459048441049', 'mtype': None, 'sender_remark': 'system', 'file_path': None, 'time': None}
[2025-07-28 23:15:19] [Flask] [INFO] wxauto收到消息: <wxauto - FriendTextMessage(WiFi密码是多...) at 0x2adcfa84210>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:15:19] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'text', 'content': 'WiFi密码是多少', 'sender': 'Jooo', 'id': '42459048441047', 'mtype': None, 'sender_remark': 'Jooo', 'file_path': None, 'time': None}
[2025-07-28 23:15:11] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:15:19)
[2025-07-28 23:15:11] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:15:19)
[2025-07-28 23:15:11] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:15:19)
[2025-07-28 23:15:11] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:15:19)
[2025-07-28 23:15:26] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:15:26] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:15:26] [Flask] [INFO] 返回 Jooo 的 2 条消息
[2025-07-28 23:15:26] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:15:19] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:15:26)
[2025-07-28 23:15:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:15:35] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:15:35] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 3.95秒
[2025-07-28 23:15:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:15:36] [Flask] [INFO] 收到请求: POST /api/chat-window/message/send
[2025-07-28 23:15:36] [Flask] [DEBUG] 请求体: {'who': 'Jooo', 'message': '您好！本公寓的WiFi信息如下：  \n\n- **WiFi名称**：以`LV`开头（例如`LV-大厅`）  \n- **密码**：`88888888`（8个8）  \n\n另外，提醒您入住后先观看房间设施的使用视频导览，以便快速熟悉各项功能。如有其他问题，欢迎随时联系！', 'clear': True, 'at_list': ['Jooo']}
[2025-07-28 23:15:36] [Flask] [DEBUG] 发送消息，当前使用的库: wxauto
[2025-07-28 23:15:36] [Flask] [DEBUG] chat_wnd类型: <class 'tuple'>, 值: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>)
[2025-07-28 23:15:36] [Flask] [ERROR] 检测到chat_wnd是tuple类型: (<wxauto - Chat object("Jooo")>, <function add_listen_chat.<locals>.message_callback at 0x000002ADCFAC7F60>)
[2025-07-28 23:15:36] [Flask] [INFO] 使用tuple的第一个元素: <class 'wxauto.wx.Chat'>
[2025-07-28 23:15:08] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:08] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:15:31)
[2025-07-28 23:15:39] [Flask] [INFO] 请求处理完成: POST /api/chat-window/message/send - 状态码: 200 - 耗时: 3.10秒
[2025-07-28 23:15:40] [Flask] [INFO] wxauto收到消息: <wxauto - SelfTextMessage(您好！本公寓的W...) at 0x2adcfc32710>, 来自聊天: <wxauto - Chat object("Jooo")>
[2025-07-28 23:15:40] [Flask] [INFO] 已将消息转换并存储到缓存: {'type': 'text', 'content': '您好！本公寓的WiFi信息如下：  \n\n- **WiFi名称**：以`LV`开头（例如`LV-大厅`）  \n- **密码**：`88888888`（8个8）  \n\n另外，提醒您入住后先观看房间设施的使用视频导览，以便快速熟悉各项功能。如有其他问题，欢迎随时联系！', 'sender': 'self', 'id': '42459048441070', 'mtype': None, 'sender_remark': 'self', 'file_path': None, 'time': None}
[2025-07-28 23:15:12] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:15:35)
[2025-07-28 23:15:40] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:15:40] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:15:40] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:15:40] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:15:40] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:15:40] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:15:40] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:15:40] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:15:45] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:15:45] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.41秒
[2025-07-28 23:15:47] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:15:47] [Flask] [INFO] 返回 Jooo 的 1 条消息
[2025-07-28 23:15:35] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:15:45)
[2025-07-28 23:15:53] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:15:53] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.66秒
[2025-07-28 23:15:26] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:15:47)
[2025-07-28 23:15:26] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:15:47)
[2025-07-28 23:15:26] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:15:47)
[2025-07-28 23:15:40] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:40] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:15:48)
[2025-07-28 23:15:54] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:15:54] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:15:54] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:15:54] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:15:56] [Flask] [INFO] 收到请求: GET /api/message/get-next-new
[2025-07-28 23:15:56] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True
[2025-07-28 23:15:56] [Flask] [DEBUG] 当前使用的库: wxauto
[2025-07-28 23:15:56] [Flask] [DEBUG] 使用wxauto参数: {}
[2025-07-28 23:15:56] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径
[2025-07-28 23:15:56] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={}
[2025-07-28 23:15:56] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 ===
[2025-07-28 23:15:56] [Flask] [DEBUG] wxauto调用参数: 无参数
[2025-07-28 23:15:45] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:15:53)
[2025-07-28 23:15:47] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:15:54)
[2025-07-28 23:16:01] [Flask] [INFO] === wxauto返回结果内容: {} ===
[2025-07-28 23:16:01] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.40秒
[2025-07-28 23:16:01] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:15:53] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> === (重复 2 次，最后: 2025-07-28 23:16:01)
[2025-07-28 23:15:54] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:16:01)
[2025-07-28 23:15:54] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:16:01)
[2025-07-28 23:15:54] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:16:01)
[2025-07-28 23:15:54] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:16:01)
[2025-07-28 23:16:08] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:16:08] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:16:08] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:16:08] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:16:09] [Flask] [INFO] === wxauto返回结果类型: <class 'dict'> ===
[2025-07-28 23:16:09] [Flask] [INFO] 请求处理完成: GET /api/message/get-next-new - 状态码: 200 - 耗时: 4.30秒
[2025-07-28 23:15:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:16:05)
[2025-07-28 23:15:56] [Flask] [INFO] 收到请求: GET /api/message/get-next-new (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [DEBUG] 处理参数: savepic=True, savevideo=False, savefile=True, savevoice=True, parseurl=True (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [DEBUG] 当前使用的库: wxauto (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [DEBUG] 使用wxauto参数: {} (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [DEBUG] 跳过wxauto保存路径设置，使用默认路径 (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [DEBUG] GetNextNewMessage调用，库: wxauto, 参数: args=(), kwargs={} (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [INFO] === wxauto GetNextNewMessage 开始调用 === (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:15:56] [Flask] [DEBUG] wxauto调用参数: 无参数 (重复 2 次，最后: 2025-07-28 23:16:04)
[2025-07-28 23:16:01] [Flask] [INFO] === wxauto返回结果内容: {} === (重复 2 次，最后: 2025-07-28 23:16:09)
[2025-07-28 23:16:01] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:16:08)
[2025-07-28 23:16:15] [Flask] [INFO] 缓存中的聊天对象: ['Jooo']
[2025-07-28 23:16:08] [Flask] [INFO] 收到请求: GET /api/message/listen/get (重复 2 次，最后: 2025-07-28 23:16:15)
[2025-07-28 23:16:08] [Flask] [INFO] 获取监听消息，使用库: wxauto (重复 2 次，最后: 2025-07-28 23:16:15)
[2025-07-28 23:16:08] [Flask] [INFO] 缓存中没有新消息 (重复 2 次，最后: 2025-07-28 23:16:15)
[2025-07-28 23:16:08] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒 (重复 2 次，最后: 2025-07-28 23:16:15)
[2025-07-28 23:16:22] [Flask] [INFO] 收到请求: GET /api/message/listen/get
[2025-07-28 23:16:22] [Flask] [INFO] 获取监听消息，使用库: wxauto
[2025-07-28 23:16:22] [Flask] [INFO] 缓存中没有新消息
[2025-07-28 23:16:22] [Flask] [INFO] 请求处理完成: GET /api/message/listen/get - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:16:15] [Flask] [INFO] 缓存中的聊天对象: ['Jooo'] (重复 2 次，最后: 2025-07-28 23:16:22)
[2025-07-28 23:16:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:16:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:16:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:17:04)
[2025-07-28 23:16:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:17:05)
[2025-07-28 23:17:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:17:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:14:32] [Flask] [INFO] 收到请求: GET /api/health (重复 59 次，最后: 2025-07-28 23:18:04)
[2025-07-28 23:14:32] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 59 次，最后: 2025-07-28 23:18:04)
[2025-07-28 23:17:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:18:04)
[2025-07-28 23:18:09] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:18:09] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:17:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:18:05)
[2025-07-28 23:18:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:18:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:18:09] [Flask] [INFO] 收到请求: GET /api/health (重复 8 次，最后: 2025-07-28 23:18:39)
[2025-07-28 23:18:09] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 8 次，最后: 2025-07-28 23:18:39)
[2025-07-28 23:18:44] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:18:44] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:18:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:19:04)
[2025-07-28 23:18:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:19:05)
[2025-07-28 23:19:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:19:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:20:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:19:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:20:04)
[2025-07-28 23:20:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:20:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:20:35)
[2025-07-28 23:21:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:20:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:21:04)
[2025-07-28 23:21:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:21:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:21:35)
[2025-07-28 23:22:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:21:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:22:04)
[2025-07-28 23:22:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:22:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:22:35)
[2025-07-28 23:23:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:22:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:23:04)
[2025-07-28 23:23:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:23:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:23:35)
[2025-07-28 23:23:56] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.01秒
[2025-07-28 23:09:28] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 315 次，最后: 2025-07-28 23:23:53)
[2025-07-28 23:23:59] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:24:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:23:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:24:04)
[2025-07-28 23:24:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:24:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:25:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:24:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:25:04)
[2025-07-28 23:25:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:24:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:25:35)
[2025-07-28 23:25:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:26:04)
[2025-07-28 23:25:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:26:05)
[2025-07-28 23:26:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:26:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:26:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:27:04)
[2025-07-28 23:26:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:27:05)
[2025-07-28 23:27:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:27:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:28:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:27:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:28:04)
[2025-07-28 23:28:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:28:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:28:35)
[2025-07-28 23:29:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:28:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:29:04)
[2025-07-28 23:29:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:29:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:29:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:30:04)
[2025-07-28 23:29:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:30:05)
[2025-07-28 23:30:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:30:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:31:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:30:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:31:04)
[2025-07-28 23:31:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:31:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:31:35)
[2025-07-28 23:32:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:31:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:32:04)
[2025-07-28 23:18:44] [Flask] [INFO] 收到请求: GET /api/health (重复 204 次，最后: 2025-07-28 23:32:14)
[2025-07-28 23:18:44] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 204 次，最后: 2025-07-28 23:32:14)
[2025-07-28 23:32:20] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:32:20] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:32:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:32:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:32:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:33:04)
[2025-07-28 23:32:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:33:05)
[2025-07-28 23:32:20] [Flask] [INFO] 收到请求: GET /api/health (重复 15 次，最后: 2025-07-28 23:33:15)
[2025-07-28 23:32:20] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 15 次，最后: 2025-07-28 23:33:15)
[2025-07-28 23:33:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:33:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:33:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:33:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:34:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:33:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:34:04)
[2025-07-28 23:33:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:34:04)
[2025-07-28 23:33:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:34:04)
[2025-07-28 23:34:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:34:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:34:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:33:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:34:35)
[2025-07-28 23:35:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:34:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:35:04)
[2025-07-28 23:34:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:35:04)
[2025-07-28 23:34:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:35:04)
[2025-07-28 23:35:25] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:35:25] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:35:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:35:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:35:35)
[2025-07-28 23:36:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:35:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:36:04)
[2025-07-28 23:35:25] [Flask] [INFO] 收到请求: GET /api/health (重复 13 次，最后: 2025-07-28 23:36:09)
[2025-07-28 23:35:25] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 13 次，最后: 2025-07-28 23:36:09)
[2025-07-28 23:36:15] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:36:15] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:36:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:36:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:36:35)
[2025-07-28 23:37:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:36:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:37:04)
[2025-07-28 23:36:15] [Flask] [INFO] 收到请求: GET /api/health (重复 15 次，最后: 2025-07-28 23:37:10)
[2025-07-28 23:36:15] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 15 次，最后: 2025-07-28 23:37:10)
[2025-07-28 23:37:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:37:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:37:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:37:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:37:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:38:04)
[2025-07-28 23:37:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:38:04)
[2025-07-28 23:37:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:38:04)
[2025-07-28 23:37:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:38:05)
[2025-07-28 23:38:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:38:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:38:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:38:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:38:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:39:04)
[2025-07-28 23:38:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:39:04)
[2025-07-28 23:38:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:39:04)
[2025-07-28 23:38:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:39:05)
[2025-07-28 23:39:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:39:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:39:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:39:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:39:47] [Flask] [INFO] 收到请求: POST /api/message/listen/remove
[2025-07-28 23:39:47] [Flask] [DEBUG] 请求体: {'nickname': 'Jooo'}
[2025-07-28 23:39:47] [Flask] [INFO] 移除监听对象: Jooo, 使用库: wxauto
[2025-07-28 23:39:47] [Flask] [INFO] RemoveListenChat调用结果: {'status': '成功', 'message': None, 'data': None}
[2025-07-28 23:39:47] [Flask] [INFO] 已从缓存中移除监听对象: Jooo
[2025-07-28 23:39:47] [Flask] [INFO] 请求处理完成: POST /api/message/listen/remove - 状态码: 200 - 耗时: 0.12秒
[2025-07-28 23:39:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:40:04)
[2025-07-28 23:39:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:40:04)
[2025-07-28 23:39:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:40:04)
[2025-07-28 23:39:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:40:05)
[2025-07-28 23:40:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:40:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:40:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:40:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:41:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:40:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:41:04)
[2025-07-28 23:40:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:41:04)
[2025-07-28 23:40:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:41:04)
[2025-07-28 23:41:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:41:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:41:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:41:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:41:35)
[2025-07-28 23:42:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:41:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:42:04)
[2025-07-28 23:41:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:42:04)
[2025-07-28 23:41:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:42:04)
[2025-07-28 23:42:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:42:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:42:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:42:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:42:35)
[2025-07-28 23:43:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:42:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:43:04)
[2025-07-28 23:42:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:43:04)
[2025-07-28 23:42:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:43:04)
[2025-07-28 23:43:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:43:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:43:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:43:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:43:35)
[2025-07-28 23:44:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:43:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:44:04)
[2025-07-28 23:43:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:44:04)
[2025-07-28 23:43:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:44:04)
[2025-07-28 23:44:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:44:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:44:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:44:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:44:35)
[2025-07-28 23:45:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:44:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:45:04)
[2025-07-28 23:44:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:45:04)
[2025-07-28 23:44:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:45:04)
[2025-07-28 23:45:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:45:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:45:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:45:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:46:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:45:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:46:04)
[2025-07-28 23:45:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:46:04)
[2025-07-28 23:45:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:46:04)
[2025-07-28 23:46:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:46:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:46:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:46:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:46:35)
[2025-07-28 23:47:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:46:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:47:04)
[2025-07-28 23:46:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:47:04)
[2025-07-28 23:46:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:47:04)
[2025-07-28 23:47:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:47:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:47:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:47:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:47:35)
[2025-07-28 23:48:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:47:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:48:04)
[2025-07-28 23:47:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:48:04)
[2025-07-28 23:47:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:48:04)
[2025-07-28 23:48:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:48:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:48:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:48:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:48:35)
[2025-07-28 23:49:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:48:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:49:04)
[2025-07-28 23:48:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:49:04)
[2025-07-28 23:48:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:49:04)
[2025-07-28 23:49:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:49:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:49:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:49:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:49:35)
[2025-07-28 23:50:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:49:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:50:04)
[2025-07-28 23:49:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:50:04)
[2025-07-28 23:49:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:50:04)
[2025-07-28 23:50:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:50:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:50:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:50:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:50:35)
[2025-07-28 23:51:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:50:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:51:04)
[2025-07-28 23:50:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:51:04)
[2025-07-28 23:50:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:51:04)
[2025-07-28 23:51:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:51:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:51:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:51:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:51:35)
[2025-07-28 23:52:04] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.01秒
[2025-07-28 23:52:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:51:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:52:04)
[2025-07-28 23:51:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:52:04)
[2025-07-28 23:51:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:52:04)
[2025-07-28 23:52:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:52:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:52:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:52:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:52:35)
[2025-07-28 23:53:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:52:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:53:04)
[2025-07-28 23:52:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:53:04)
[2025-07-28 23:52:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:53:04)
[2025-07-28 23:53:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:53:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:53:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:53:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:54:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:53:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:54:04)
[2025-07-28 23:53:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:54:04)
[2025-07-28 23:53:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:54:04)
[2025-07-28 23:54:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:54:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:54:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:54:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:54:35)
[2025-07-28 23:55:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:54:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:55:04)
[2025-07-28 23:54:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:55:04)
[2025-07-28 23:54:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:55:04)
[2025-07-28 23:55:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:55:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:55:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:55:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:55:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:56:04)
[2025-07-28 23:55:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:56:04)
[2025-07-28 23:55:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:56:04)
[2025-07-28 23:55:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:56:05)
[2025-07-28 23:56:27] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.01秒
[2025-07-28 23:23:59] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒 (重复 708 次，最后: 2025-07-28 23:56:24)
[2025-07-28 23:56:30] [Flask] [INFO] 请求处理完成: GET /api/wechat/status - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:56:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:56:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:56:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:56:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:56:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:57:04)
[2025-07-28 23:56:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:57:04)
[2025-07-28 23:56:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:57:04)
[2025-07-28 23:56:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒 (重复 2 次，最后: 2025-07-28 23:57:05)
[2025-07-28 23:57:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:57:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:57:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:57:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:58:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:57:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:58:04)
[2025-07-28 23:57:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:58:04)
[2025-07-28 23:57:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:58:04)
[2025-07-28 23:58:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:58:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:58:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:58:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒
[2025-07-28 23:59:05] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.00秒
[2025-07-28 23:58:34] [Flask] [INFO] 收到请求: GET /api/system/resources (重复 2 次，最后: 2025-07-28 23:59:04)
[2025-07-28 23:58:34] [Flask] [INFO] 收到请求: GET /api/health (重复 4 次，最后: 2025-07-28 23:59:04)
[2025-07-28 23:58:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒 (重复 4 次，最后: 2025-07-28 23:59:04)
[2025-07-28 23:59:34] [Flask] [INFO] 收到请求: GET /api/system/resources
[2025-07-28 23:59:34] [Flask] [INFO] 收到请求: GET /api/health
[2025-07-28 23:59:34] [Flask] [INFO] 请求处理完成: GET /api/health - 状态码: 200 - 耗时: 0.00秒
[2025-07-28 23:58:35] [Flask] [INFO] 请求处理完成: GET /api/system/resources - 状态码: 200 - 耗时: 1.01秒 (重复 2 次，最后: 2025-07-28 23:59:35)
