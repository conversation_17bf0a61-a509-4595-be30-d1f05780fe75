2025-07-31 21:22:50.450 | INFO     | wxauto_mgt.utils.logging:setup_logging:80 | 日志系统初始化完成，日志文件：C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\data\logs\wxauto_mgt_20250731.log
2025-07-31 21:22:50.450 | INFO     | __main__:init_services:163 | 正在初始化SSL配置...
2025-07-31 21:22:50.451 | INFO     | wxauto_mgt.utils.ssl_config:init_ssl:107 | 初始化SSL配置...
2025-07-31 21:22:50.451 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:27 | 使用certifi证书: C:\Users\<USER>\AppData\Local\Temp\_MEI67962\certifi\cacert.pem
2025-07-31 21:22:50.452 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:55 | SSL证书路径已设置: C:\Users\<USER>\AppData\Local\Temp\_MEI67962\certifi\cacert.pem
2025-07-31 21:22:51.038 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:64 | SSL上下文创建成功
2025-07-31 21:22:54.414 | INFO     | wxauto_mgt.utils.ssl_config:verify_ssl_setup:91 | SSL连接测试成功: https://httpbin.org/get
2025-07-31 21:22:54.416 | INFO     | wxauto_mgt.utils.ssl_config:init_ssl:116 | SSL配置和验证完成
2025-07-31 21:22:54.416 | INFO     | __main__:init_services:166 | SSL配置初始化成功
2025-07-31 21:22:54.586 | INFO     | __main__:init_services:178 | 正在初始化配置管理器...
2025-07-31 21:22:54.661 | INFO     | __main__:init_services:181 | 配置管理器初始化完成
2025-07-31 21:22:54.662 | INFO     | __main__:init_services:184 | 正在加载实例配置...
2025-07-31 21:22:54.671 | INFO     | __main__:init_services:197 | 从数据库中获取到 1 个实例
2025-07-31 21:22:54.671 | INFO     | __main__:init_services:216 | 正在加载实例: wxauto_c032f6ba (本机)
2025-07-31 21:22:54.672 | INFO     | __main__:init_services:218 | 已加载实例: wxauto_c032f6ba
2025-07-31 21:22:54.673 | INFO     | __main__:init_services:232 | 正在启动消息监听...
2025-07-31 21:22:54.685 | INFO     | __main__:init_services:234 | 消息监听服务已启动
2025-07-31 21:22:54.685 | INFO     | __main__:init_services:244 | 正在初始化消息投递服务...
2025-07-31 21:22:54.718 | INFO     | __main__:init_services:248 | 正在启动消息投递服务...
2025-07-31 21:22:54.719 | INFO     | __main__:init_services:250 | 消息投递服务已启动
2025-07-31 21:22:54.719 | INFO     | __main__:init_services:255 | 服务初始化完成
2025-07-31 21:22:54.789 | DEBUG    | wxauto_mgt.ui.components.message_panel:__init__:141 | 日志处理器初始化完成，关键事件过滤词已设置
2025-07-31 21:22:54.789 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2722 | 消息监听界面已启动，日志系统已连接
2025-07-31 21:22:54.790 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2728 | 当前监听对象数量: 0
2025-07-31 21:22:54.790 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2729 | 轮询间隔: 5秒, 超时时间: 30分钟
2025-07-31 21:22:54.791 | DEBUG    | wxauto_mgt.ui.components.message_panel:_init_logging:2734 | 日志系统初始化完成
2025-07-31 21:22:54.791 | DEBUG    | wxauto_mgt.ui.components.message_panel:__init__:886 | 消息监听面板已初始化
2025-07-31 21:22:54.799 | DEBUG    | wxauto_mgt.web.config:_load_from_store:62 | 使用默认Web服务配置
2025-07-31 21:22:54.799 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:294 | 已设置端口号: 8080
2025-07-31 21:22:54.799 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:300 | 已设置主机地址: 0.0.0.0
2025-07-31 21:22:54.800 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:305 | 已设置自动启动: True
2025-07-31 21:22:54.800 | INFO     | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:318 | Web服务面板已加载配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-31 21:22:54.806 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:284 | 事件循环未运行，无法获取实例名称
2025-07-31 21:22:54.808 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:547 | 执行查询: SELECT * FROM instances WHERE enabled = 1
2025-07-31 21:22:54.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:22:54.811 | INFO     | wxauto_mgt.ui.main_window:__init__:56 | 设置延迟配置保存定时器
2025-07-31 21:22:54.812 | INFO     | wxauto_mgt.ui.utils.ui_monitor:start_monitoring:58 | UI响应性监控已启动，检查间隔: 100ms
2025-07-31 21:22:54.812 | INFO     | wxauto_mgt.ui.main_window:__init__:62 | 主窗口已初始化
2025-07-31 21:22:55.081 | DEBUG    | __main__:main:396 | 当前平台不支持add_signal_handler，跳过信号处理设置
2025-07-31 21:22:55.082 | INFO     | __main__:main:400 | 程序已启动
2025-07-31 21:22:55.084 | INFO     | wxauto_mgt.ui.components.message_panel:_update_status_count:1976 | 消息统计: 已处理: 0, 未处理: 0, 总计: 0
2025-07-31 21:22:55.085 | INFO     | wxauto_mgt.ui.components.message_panel:_update_status_count:1976 | 消息统计: 已处理: 0, 未处理: 0, 总计: 0
2025-07-31 21:22:55.120 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:549 | 查询结果: 1 个实例
2025-07-31 21:22:55.120 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:571 | 加载了 1 个实例
2025-07-31 21:22:55.121 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:575 | 实例 1: ID=wxauto_c032f6ba, 名称=本机
2025-07-31 21:22:55.635 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:271 | 获取实例名称超时: wxauto_c032f6ba
2025-07-31 21:22:56.639 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:594 | 已添加实例卡片: wxauto_c032f6ba
2025-07-31 21:22:56.646 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1313 | 创建实例面板: wxauto_c032f6ba
2025-07-31 21:22:56.647 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:22:56.655 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:22:56.660 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:22:56.696 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_c032f6ba 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-31 21:22:56.701 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:22:56.702 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:139 | 实例 wxauto_c032f6ba 正在更新中，跳过
2025-07-31 21:22:56.779 | INFO     | wxauto_mgt.ui.components.service_platform_panel:refresh_platforms:130 | 开始刷新服务平台列表...
2025-07-31 21:22:56.779 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh_platforms:177 | 启动异步刷新线程...
2025-07-31 21:22:56.780 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:run_async_task:137 | 创建新的事件循环...
2025-07-31 21:22:56.782 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:143 | 开始异步刷新任务...
2025-07-31 21:22:56.783 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:156 | 获取所有平台...
2025-07-31 21:22:56.788 | INFO     | wxauto_mgt.ui.components.service_platform_panel:refresh:158 | 获取到 2 个平台
2025-07-31 21:22:56.789 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:161 | 发出platforms_loaded信号...
2025-07-31 21:22:56.789 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:run_async_task:166 | 异步刷新任务完成
2025-07-31 21:22:56.799 | INFO     | wxauto_mgt.ui.components.service_platform_panel:_update_platform_table:197 | 刷新平台列表成功，共 2 个平台
2025-07-31 21:22:56.802 | INFO     | wxauto_mgt.ui.main_window:start_delayed_save:53 | 启动延迟配置保存任务（2秒后执行）
2025-07-31 21:22:56.803 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:354 | 开始执行延迟配置保存任务
2025-07-31 21:22:56.804 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:357 | 正在获取实例配置...
2025-07-31 21:22:56.814 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:368 | 正在获取Web服务配置...
2025-07-31 21:22:56.815 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:370 | 获取到Web服务配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-31 21:22:56.815 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:373 | 加载Web服务配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-31 21:22:56.816 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:377 | 正在刷新Web服务面板UI...
2025-07-31 21:22:56.816 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:355 | 刷新端口号: 8080
2025-07-31 21:22:56.817 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:360 | 刷新主机地址: 0.0.0.0
2025-07-31 21:22:56.817 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:365 | 刷新自动启动: True
2025-07-31 21:22:56.817 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:373 | 刷新密码状态: 未设置
2025-07-31 21:22:56.818 | INFO     | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:376 | Web服务面板已从数据库刷新配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-31 21:22:56.818 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:379 | 已更新Web服务面板UI配置
2025-07-31 21:22:56.819 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:385 | 检测到Web服务自动启动配置
2025-07-31 21:22:56.823 | INFO     | wxauto_mgt.web.config:save_config:197 | Web服务配置已保存: host=0.0.0.0, port=8080, auto_start=True
2025-07-31 21:22:57.473 | DEBUG    | wxauto_mgt.web.config:initialize:77 | Web服务配置初始化 - 数据库路径: C:\Users\<USER>\AppData\Local\Temp\_MEI67962\data\wxauto_mgt.db
2025-07-31 21:22:57.473 | DEBUG    | wxauto_mgt.web.config:initialize:82 | Web服务配置初始化 - 数据库文件存在
2025-07-31 21:22:57.476 | DEBUG    | wxauto_mgt.web.config:initialize:88 | Web服务配置初始化 - 从配置存储读取: {}
2025-07-31 21:22:57.477 | DEBUG    | wxauto_mgt.web.config:initialize:89 | Web服务配置初始化 - 配置类型: <class 'dict'>
2025-07-31 21:22:57.477 | DEBUG    | wxauto_mgt.web.config:initialize:93 | Web服务配置初始化 - 配置字段: []
2025-07-31 21:22:57.478 | DEBUG    | wxauto_mgt.web.config:initialize:94 | Web服务配置初始化 - 是否包含密码: False
2025-07-31 21:22:57.480 | ERROR    | wxauto_mgt.web.config:initialize:112 | Web服务配置初始化 - 数据库直接查询失败: no such table: configs
2025-07-31 21:22:57.480 | WARNING  | wxauto_mgt.web.config:initialize:116 | Web服务配置初始化 - 读取到空配置，尝试直接查询数据库
2025-07-31 21:22:57.482 | ERROR    | wxauto_mgt.web.config:initialize:131 | Web服务配置初始化 - 直接查询数据库失败: no such table: configs
2025-07-31 21:22:57.483 | DEBUG    | wxauto_mgt.web.config:_apply_config:157 | 已加载Web服务配置: host=0.0.0.0, port=8080, auto_start=True, has_password=False
2025-07-31 21:22:57.487 | INFO     | wxauto_mgt.web.server:create_app:74 | 静态文件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI67962\wxauto_mgt\web\static
2025-07-31 21:22:57.488 | INFO     | wxauto_mgt.web.server:create_app:86 | 模板文件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI67962\wxauto_mgt\web\templates
2025-07-31 21:22:57.521 | INFO     | wxauto_mgt.web.routes:register_routes:57 | 注册Web路由
2025-07-31 21:22:57.524 | INFO     | wxauto_mgt.web.server:run_server:234 | Web服务器启动中，地址: http://0.0.0.0:8080
2025-07-31 21:22:57.546 | DEBUG    | wxauto_mgt.web.config:initialize:77 | Web服务配置初始化 - 数据库路径: C:\Users\<USER>\AppData\Local\Temp\_MEI67962\data\wxauto_mgt.db
2025-07-31 21:22:57.548 | DEBUG    | wxauto_mgt.web.config:initialize:82 | Web服务配置初始化 - 数据库文件存在
2025-07-31 21:22:57.550 | DEBUG    | wxauto_mgt.web.config:initialize:88 | Web服务配置初始化 - 从配置存储读取: {}
2025-07-31 21:22:57.550 | DEBUG    | wxauto_mgt.web.config:initialize:89 | Web服务配置初始化 - 配置类型: <class 'dict'>
2025-07-31 21:22:57.551 | DEBUG    | wxauto_mgt.web.config:initialize:93 | Web服务配置初始化 - 配置字段: []
2025-07-31 21:22:57.551 | DEBUG    | wxauto_mgt.web.config:initialize:94 | Web服务配置初始化 - 是否包含密码: False
2025-07-31 21:22:57.553 | ERROR    | wxauto_mgt.web.config:initialize:112 | Web服务配置初始化 - 数据库直接查询失败: no such table: configs
2025-07-31 21:22:57.554 | WARNING  | wxauto_mgt.web.config:initialize:116 | Web服务配置初始化 - 读取到空配置，尝试直接查询数据库
2025-07-31 21:22:57.556 | ERROR    | wxauto_mgt.web.config:initialize:131 | Web服务配置初始化 - 直接查询数据库失败: no such table: configs
2025-07-31 21:22:57.557 | DEBUG    | wxauto_mgt.web.config:_apply_config:157 | 已加载Web服务配置: host=0.0.0.0, port=8080, auto_start=True, has_password=False
2025-07-31 21:22:57.557 | INFO     | wxauto_mgt.web.server:startup_event:101 | Web服务配置初始化完成
2025-07-31 21:22:57.576 | INFO     | wxauto_mgt.web.security:initialize_security:40 | 已加载JWT密钥
2025-07-31 21:22:57.576 | INFO     | wxauto_mgt.web.server:startup_event:106 | 安全模块初始化完成
2025-07-31 21:22:58.524 | INFO     | wxauto_mgt.web:start_web_service:96 | Web服务已启动，地址: http://0.0.0.0:8080
2025-07-31 21:22:58.524 | INFO     | wxauto_mgt.ui.components.web_service_panel:_start_web_service:466 | Web服务已启动，地址: http://0.0.0.0:8080
2025-07-31 21:22:58.525 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:398 | 延迟配置保存任务完成
2025-07-31 21:22:58.531 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:22:58.533 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_c032f6ba 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-31 21:22:58.535 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:22:58.539 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:22:58.544 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:22:58.544 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:23:24.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:23:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:23:24.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:23:25.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:23:25.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:23:25.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:23:25.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:23:25.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:23:54.770 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:23:54.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:23:54.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:23:55.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:23:55.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:23:55.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:23:55.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:23:55.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:24:24.771 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:24:24.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:24:24.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:24:25.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:24:25.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:24:25.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:24:25.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:24:25.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:24:54.769 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:24:54.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:24:54.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:24:55.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:24:55.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:24:55.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:24:55.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:24:55.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:25:24.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:25:24.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:25:24.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:25:25.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:25:25.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:25:25.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:25:25.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:25:25.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:25:54.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:25:54.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:25:54.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:25:55.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:25:55.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:25:55.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:25:55.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:25:55.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:26:24.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:26:24.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:26:24.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:26:25.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:26:25.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:26:25.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:26:25.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:26:25.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:26:54.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:26:54.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:26:54.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:26:55.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:26:55.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:26:55.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:26:55.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:26:55.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:27:24.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:27:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:27:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:27:25.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:27:25.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:27:25.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:27:25.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:27:25.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:27:54.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:27:54.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:27:54.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:27:55.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:27:55.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:27:55.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:27:55.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:27:55.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:28:24.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:28:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:28:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:28:25.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:28:25.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:28:25.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:28:25.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:28:25.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:28:54.771 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:28:54.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:28:54.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:28:55.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:28:55.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:28:55.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:28:55.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:28:55.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:29:24.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:29:24.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:29:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:29:25.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:29:25.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:29:25.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:29:25.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:29:25.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:29:54.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:29:54.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:29:54.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:29:55.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:29:55.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:29:55.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:29:55.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:29:55.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:30:24.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:30:24.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:30:24.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:30:25.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:30:25.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:30:25.819 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:30:25.824 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:30:25.825 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:30:54.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:30:54.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:30:54.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:30:55.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:30:55.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:30:55.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:30:55.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:30:55.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:31:24.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:31:24.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:31:24.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:31:25.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:31:25.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:31:25.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:31:25.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:31:25.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:31:54.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:31:54.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:31:54.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:31:55.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:31:55.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:31:55.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:31:55.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:31:55.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:32:24.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:32:24.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:32:24.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:32:25.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:32:25.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:32:25.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:32:25.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:32:25.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:32:54.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:32:54.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:32:54.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:32:55.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:32:55.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:32:55.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:32:55.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:32:55.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:33:24.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:33:24.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:33:24.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:33:25.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:33:25.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:33:25.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:33:25.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:33:25.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:33:54.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-31 21:33:54.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-31 21:33:54.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-31 21:33:55.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-31 21:33:55.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-31 21:33:55.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-31 21:33:55.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-31 21:33:55.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-31 21:34:00.379 | INFO     | wxauto_mgt.ui.main_window:closeEvent:309 | 用户请求关闭应用程序，开始强制关闭流程
2025-07-31 21:34:00.380 | INFO     | wxauto_mgt.ui.utils.ui_monitor:stop_monitoring:65 | UI响应性监控已停止
2025-07-31 21:34:00.380 | INFO     | wxauto_mgt.ui.main_window:closeEvent:323 | 强制停止事件循环
2025-07-31 21:34:00.389 | INFO     | __main__:start_force_exit_monitor:348 | 强制退出监控已启动（10秒超时）
2025-07-31 21:34:00.390 | INFO     | __main__:main:408 | 程序正在关闭，开始强制清理...
2025-07-31 21:34:00.391 | INFO     | __main__:main:413 | 发现 5 个待处理任务，强制取消...
2025-07-31 21:34:00.407 | INFO     | __main__:cleanup_services_sync:270 | 正在停止Web服务...
2025-07-31 21:34:00.408 | INFO     | wxauto_mgt.web.server:force_stop_server:312 | 强制停止Web服务器...
2025-07-31 21:34:00.409 | INFO     | wxauto_mgt.web.server:force_stop_server:323 | 已设置强制停止标志
2025-07-31 21:34:00.409 | INFO     | __main__:cleanup_services_sync:278 | 等待Web服务线程结束...
2025-07-31 21:34:00.626 | INFO     | wxauto_mgt.web.server:shutdown_event:116 | Web应用正在关闭...
2025-07-31 21:34:00.732 | INFO     | wxauto_mgt.web.server:shutdown_event:135 | Web应用关闭清理完成
2025-07-31 21:34:00.734 | INFO     | wxauto_mgt.web.server:run_server:266 | Web服务器已停止
2025-07-31 21:34:00.735 | INFO     | __main__:cleanup_services_sync:283 | Web服务线程已结束
2025-07-31 21:34:00.735 | INFO     | __main__:cleanup_services_sync:288 | Web服务已停止
2025-07-31 21:34:00.736 | INFO     | __main__:cleanup_services_sync:297 | 强制停止消息投递服务...
2025-07-31 21:34:00.736 | INFO     | __main__:cleanup_services_sync:305 | 消息投递服务已强制停止
2025-07-31 21:34:00.736 | INFO     | __main__:cleanup_services_sync:313 | 强制停止消息监听服务...
2025-07-31 21:34:00.736 | INFO     | __main__:cleanup_services_sync:322 | 消息监听服务已强制停止
2025-07-31 21:34:00.737 | INFO     | __main__:main:432 | 强制清理完成
