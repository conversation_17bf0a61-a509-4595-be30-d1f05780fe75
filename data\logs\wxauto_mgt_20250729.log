2025-07-29 20:01:27.901 | INFO     | wxauto_mgt.utils.logging:setup_logging:80 | 日志系统初始化完成，日志文件：C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\data\logs\wxauto_mgt_20250729.log
2025-07-29 20:01:27.901 | INFO     | __main__:init_services:163 | 正在初始化SSL配置...
2025-07-29 20:01:27.901 | INFO     | wxauto_mgt.utils.ssl_config:init_ssl:107 | 初始化SSL配置...
2025-07-29 20:01:27.902 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:27 | 使用certifi证书: C:\Users\<USER>\AppData\Local\Temp\_MEI179282\certifi\cacert.pem
2025-07-29 20:01:27.902 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:55 | SSL证书路径已设置: C:\Users\<USER>\AppData\Local\Temp\_MEI179282\certifi\cacert.pem
2025-07-29 20:01:28.428 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:64 | SSL上下文创建成功
2025-07-29 20:01:30.436 | INFO     | wxauto_mgt.utils.ssl_config:verify_ssl_setup:91 | SSL连接测试成功: https://httpbin.org/get
2025-07-29 20:01:30.437 | INFO     | wxauto_mgt.utils.ssl_config:init_ssl:116 | SSL配置和验证完成
2025-07-29 20:01:30.437 | INFO     | __main__:init_services:166 | SSL配置初始化成功
2025-07-29 20:01:30.611 | INFO     | __main__:init_services:178 | 正在初始化配置管理器...
2025-07-29 20:01:30.678 | INFO     | __main__:init_services:181 | 配置管理器初始化完成
2025-07-29 20:01:30.678 | INFO     | __main__:init_services:184 | 正在加载实例配置...
2025-07-29 20:01:30.685 | INFO     | __main__:init_services:197 | 从数据库中获取到 1 个实例
2025-07-29 20:01:30.685 | INFO     | __main__:init_services:216 | 正在加载实例: wxauto_d3d71ec7 (本机)
2025-07-29 20:01:30.686 | INFO     | __main__:init_services:218 | 已加载实例: wxauto_d3d71ec7
2025-07-29 20:01:30.686 | INFO     | __main__:init_services:232 | 正在启动消息监听...
2025-07-29 20:01:30.694 | INFO     | __main__:init_services:234 | 消息监听服务已启动
2025-07-29 20:01:30.695 | INFO     | __main__:init_services:244 | 正在初始化消息投递服务...
2025-07-29 20:01:30.719 | INFO     | __main__:init_services:248 | 正在启动消息投递服务...
2025-07-29 20:01:30.719 | INFO     | __main__:init_services:250 | 消息投递服务已启动
2025-07-29 20:01:30.719 | INFO     | __main__:init_services:255 | 服务初始化完成
2025-07-29 20:01:30.775 | DEBUG    | wxauto_mgt.ui.components.message_panel:__init__:141 | 日志处理器初始化完成，关键事件过滤词已设置
2025-07-29 20:01:30.775 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2722 | 消息监听界面已启动，日志系统已连接
2025-07-29 20:01:30.777 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2728 | 当前监听对象数量: 0
2025-07-29 20:01:30.777 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2729 | 轮询间隔: 5秒, 超时时间: 30分钟
2025-07-29 20:01:30.777 | DEBUG    | wxauto_mgt.ui.components.message_panel:_init_logging:2734 | 日志系统初始化完成
2025-07-29 20:01:30.778 | DEBUG    | wxauto_mgt.ui.components.message_panel:__init__:886 | 消息监听面板已初始化
2025-07-29 20:01:30.785 | DEBUG    | wxauto_mgt.web.config:_load_from_store:62 | 使用默认Web服务配置
2025-07-29 20:01:30.785 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:294 | 已设置端口号: 8080
2025-07-29 20:01:30.786 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:300 | 已设置主机地址: 0.0.0.0
2025-07-29 20:01:30.786 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:305 | 已设置自动启动: True
2025-07-29 20:01:30.787 | INFO     | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:318 | Web服务面板已加载配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 20:01:30.792 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:284 | 事件循环未运行，无法获取实例名称
2025-07-29 20:01:30.793 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:547 | 执行查询: SELECT * FROM instances WHERE enabled = 1
2025-07-29 20:01:30.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:01:30.796 | INFO     | wxauto_mgt.ui.main_window:__init__:56 | 设置延迟配置保存定时器
2025-07-29 20:01:30.796 | INFO     | wxauto_mgt.ui.utils.ui_monitor:start_monitoring:58 | UI响应性监控已启动，检查间隔: 100ms
2025-07-29 20:01:30.797 | INFO     | wxauto_mgt.ui.main_window:__init__:62 | 主窗口已初始化
2025-07-29 20:01:30.932 | DEBUG    | __main__:main:396 | 当前平台不支持add_signal_handler，跳过信号处理设置
2025-07-29 20:01:30.932 | INFO     | __main__:main:400 | 程序已启动
2025-07-29 20:01:30.935 | INFO     | wxauto_mgt.ui.components.message_panel:_update_status_count:1976 | 消息统计: 已处理: 0, 未处理: 0, 总计: 0
2025-07-29 20:01:30.935 | INFO     | wxauto_mgt.ui.components.message_panel:_update_status_count:1976 | 消息统计: 已处理: 0, 未处理: 0, 总计: 0
2025-07-29 20:01:30.973 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1313 | 创建实例面板: wxauto_d3d71ec7
2025-07-29 20:01:30.973 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:01:30.978 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:01:30.979 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:549 | 查询结果: 1 个实例
2025-07-29 20:01:30.980 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:571 | 加载了 1 个实例
2025-07-29 20:01:30.981 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:575 | 实例 1: ID=wxauto_d3d71ec7, 名称=本机
2025-07-29 20:01:31.487 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:271 | 获取实例名称超时: wxauto_d3d71ec7
2025-07-29 20:01:32.504 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:594 | 已添加实例卡片: wxauto_d3d71ec7
2025-07-29 20:01:33.017 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:271 | 获取实例名称超时: wxauto_d3d71ec7
2025-07-29 20:01:33.018 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:01:33.025 | INFO     | wxauto_mgt.ui.components.service_platform_panel:refresh_platforms:130 | 开始刷新服务平台列表...
2025-07-29 20:01:33.026 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh_platforms:177 | 启动异步刷新线程...
2025-07-29 20:01:33.027 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:run_async_task:137 | 创建新的事件循环...
2025-07-29 20:01:33.029 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:143 | 开始异步刷新任务...
2025-07-29 20:01:33.029 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:156 | 获取所有平台...
2025-07-29 20:01:33.033 | INFO     | wxauto_mgt.ui.components.service_platform_panel:refresh:158 | 获取到 2 个平台
2025-07-29 20:01:33.035 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:161 | 发出platforms_loaded信号...
2025-07-29 20:01:33.035 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:run_async_task:166 | 异步刷新任务完成
2025-07-29 20:01:33.037 | INFO     | wxauto_mgt.ui.main_window:start_delayed_save:53 | 启动延迟配置保存任务（2秒后执行）
2025-07-29 20:01:33.046 | INFO     | wxauto_mgt.ui.components.service_platform_panel:_update_platform_table:197 | 刷新平台列表成功，共 2 个平台
2025-07-29 20:01:33.047 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:354 | 开始执行延迟配置保存任务
2025-07-29 20:01:33.047 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:357 | 正在获取实例配置...
2025-07-29 20:01:33.068 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:01:33.069 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:139 | 实例 wxauto_d3d71ec7 正在更新中，跳过
2025-07-29 20:01:33.071 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:368 | 正在获取Web服务配置...
2025-07-29 20:01:33.072 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:370 | 获取到Web服务配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 20:01:33.072 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:373 | 加载Web服务配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 20:01:33.073 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:377 | 正在刷新Web服务面板UI...
2025-07-29 20:01:33.073 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:355 | 刷新端口号: 8080
2025-07-29 20:01:33.073 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:360 | 刷新主机地址: 0.0.0.0
2025-07-29 20:01:33.074 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:365 | 刷新自动启动: True
2025-07-29 20:01:33.074 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:373 | 刷新密码状态: 未设置
2025-07-29 20:01:33.075 | INFO     | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:376 | Web服务面板已从数据库刷新配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 20:01:33.075 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:379 | 已更新Web服务面板UI配置
2025-07-29 20:01:33.075 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:385 | 检测到Web服务自动启动配置
2025-07-29 20:01:33.078 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_d3d71ec7 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-29 20:01:33.085 | INFO     | wxauto_mgt.web.config:save_config:197 | Web服务配置已保存: host=0.0.0.0, port=8080, auto_start=True
2025-07-29 20:01:33.452 | DEBUG    | wxauto_mgt.web.config:initialize:77 | Web服务配置初始化 - 数据库路径: C:\Users\<USER>\AppData\Local\Temp\_MEI179282\data\wxauto_mgt.db
2025-07-29 20:01:33.452 | DEBUG    | wxauto_mgt.web.config:initialize:82 | Web服务配置初始化 - 数据库文件存在
2025-07-29 20:01:33.480 | DEBUG    | wxauto_mgt.web.config:initialize:88 | Web服务配置初始化 - 从配置存储读取: {}
2025-07-29 20:01:33.480 | DEBUG    | wxauto_mgt.web.config:initialize:89 | Web服务配置初始化 - 配置类型: <class 'dict'>
2025-07-29 20:01:33.481 | DEBUG    | wxauto_mgt.web.config:initialize:93 | Web服务配置初始化 - 配置字段: []
2025-07-29 20:01:33.481 | DEBUG    | wxauto_mgt.web.config:initialize:94 | Web服务配置初始化 - 是否包含密码: False
2025-07-29 20:01:33.483 | ERROR    | wxauto_mgt.web.config:initialize:112 | Web服务配置初始化 - 数据库直接查询失败: no such table: configs
2025-07-29 20:01:33.484 | WARNING  | wxauto_mgt.web.config:initialize:116 | Web服务配置初始化 - 读取到空配置，尝试直接查询数据库
2025-07-29 20:01:33.487 | ERROR    | wxauto_mgt.web.config:initialize:131 | Web服务配置初始化 - 直接查询数据库失败: no such table: configs
2025-07-29 20:01:33.487 | DEBUG    | wxauto_mgt.web.config:_apply_config:157 | 已加载Web服务配置: host=0.0.0.0, port=8080, auto_start=True, has_password=False
2025-07-29 20:01:33.491 | INFO     | wxauto_mgt.web.server:create_app:74 | 静态文件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI179282\wxauto_mgt\web\static
2025-07-29 20:01:33.491 | INFO     | wxauto_mgt.web.server:create_app:86 | 模板文件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI179282\wxauto_mgt\web\templates
2025-07-29 20:01:33.521 | INFO     | wxauto_mgt.web.routes:register_routes:57 | 注册Web路由
2025-07-29 20:01:33.523 | INFO     | wxauto_mgt.web.server:run_server:234 | Web服务器启动中，地址: http://0.0.0.0:8080
2025-07-29 20:01:33.541 | DEBUG    | wxauto_mgt.web.config:initialize:77 | Web服务配置初始化 - 数据库路径: C:\Users\<USER>\AppData\Local\Temp\_MEI179282\data\wxauto_mgt.db
2025-07-29 20:01:33.541 | DEBUG    | wxauto_mgt.web.config:initialize:82 | Web服务配置初始化 - 数据库文件存在
2025-07-29 20:01:33.543 | DEBUG    | wxauto_mgt.web.config:initialize:88 | Web服务配置初始化 - 从配置存储读取: {}
2025-07-29 20:01:33.543 | DEBUG    | wxauto_mgt.web.config:initialize:89 | Web服务配置初始化 - 配置类型: <class 'dict'>
2025-07-29 20:01:33.543 | DEBUG    | wxauto_mgt.web.config:initialize:93 | Web服务配置初始化 - 配置字段: []
2025-07-29 20:01:33.543 | DEBUG    | wxauto_mgt.web.config:initialize:94 | Web服务配置初始化 - 是否包含密码: False
2025-07-29 20:01:33.545 | ERROR    | wxauto_mgt.web.config:initialize:112 | Web服务配置初始化 - 数据库直接查询失败: no such table: configs
2025-07-29 20:01:33.545 | WARNING  | wxauto_mgt.web.config:initialize:116 | Web服务配置初始化 - 读取到空配置，尝试直接查询数据库
2025-07-29 20:01:33.546 | ERROR    | wxauto_mgt.web.config:initialize:131 | Web服务配置初始化 - 直接查询数据库失败: no such table: configs
2025-07-29 20:01:33.546 | DEBUG    | wxauto_mgt.web.config:_apply_config:157 | 已加载Web服务配置: host=0.0.0.0, port=8080, auto_start=True, has_password=False
2025-07-29 20:01:33.547 | INFO     | wxauto_mgt.web.server:startup_event:101 | Web服务配置初始化完成
2025-07-29 20:01:33.563 | INFO     | wxauto_mgt.web.security:initialize_security:40 | 已加载JWT密钥
2025-07-29 20:01:33.563 | INFO     | wxauto_mgt.web.server:startup_event:106 | 安全模块初始化完成
2025-07-29 20:01:34.524 | INFO     | wxauto_mgt.web:start_web_service:96 | Web服务已启动，地址: http://0.0.0.0:8080
2025-07-29 20:01:34.524 | INFO     | wxauto_mgt.ui.components.web_service_panel:_start_web_service:466 | Web服务已启动，地址: http://0.0.0.0:8080
2025-07-29 20:01:34.525 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:398 | 延迟配置保存任务完成
2025-07-29 20:01:34.530 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:01:34.535 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_d3d71ec7 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-29 20:01:34.537 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:01:34.542 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:01:34.546 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:01:34.546 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:01:59.239 | INFO     | wxauto_mgt.web.api:initialize_managers:123 | 所有管理器初始化完成
2025-07-29 20:01:59.344 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:01:59.344 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:02:00.765 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:02:00.768 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:02:00.769 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:02:02.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:02:02.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:02:02.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:02:02.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:02:02.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:02:06.435 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:02:06.442 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:02:06.442 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:02:06.456 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:02:13.635 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:02:13.648 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:02:30.765 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:02:30.769 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:02:30.769 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:02:32.767 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:02:32.770 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:02:32.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:02:32.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:02:32.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:03:00.768 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:03:00.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:03:00.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:03:02.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:03:02.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:03:02.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:03:02.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:03:02.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:03:06.099 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:03:06.104 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:03:06.104 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:03:06.114 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:03:06.114 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:03:06.121 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:03:13.298 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:03:13.312 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:03:30.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:03:30.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:03:30.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:03:32.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:03:32.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:03:32.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:03:32.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:03:32.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:04:00.768 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:04:00.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:04:00.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:04:02.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:04:02.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:04:02.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:04:02.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:04:02.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:04:06.097 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:04:06.101 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:04:06.101 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:04:06.112 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:04:06.113 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:04:06.132 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:04:13.305 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:04:13.317 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:04:30.766 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:04:30.770 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:04:30.771 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:04:32.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:04:32.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:04:32.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:04:32.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:04:32.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:05:00.767 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:05:00.771 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:05:00.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:05:02.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:05:02.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:05:02.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:05:02.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:05:02.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:05:06.102 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:05:06.109 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:05:06.109 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:05:06.123 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:05:06.123 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:05:06.131 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:05:13.310 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:05:13.322 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:05:30.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:05:30.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:05:30.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:05:32.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:05:32.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:05:32.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:05:32.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:05:32.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:06:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:06:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:06:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:06:02.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:06:02.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:06:02.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:06:02.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:06:02.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:06:06.103 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:06:06.108 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:06:06.108 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:06:06.119 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:06:06.120 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:06:06.128 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:06:13.276 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:06:13.289 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:06:30.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:06:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:06:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:06:32.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:06:32.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:06:32.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:06:32.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:06:32.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:07:00.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:07:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:07:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:07:02.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:07:02.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:07:02.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:07:02.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:07:02.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:07:06.127 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:07:06.130 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:07:06.131 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:07:06.143 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:07:06.145 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:07:06.154 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:07:13.339 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Read timed out. (read timeout=5)
2025-07-29 20:07:13.353 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:07:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:07:30.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:07:30.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:07:32.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'timeout', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:07:32.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:07:32.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:07:32.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:07:32.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:08:00.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:08:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:08:00.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:08:01.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:08:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:08:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:08:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:08:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:08:03.117 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D9D9673D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:08:03.121 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:08:03.121 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:08:03.132 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:08:03.134 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:08:03.152 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:08:07.347 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A10590>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:08:07.359 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:08:30.768 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:08:30.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:08:30.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:08:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:08:31.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:08:31.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:08:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:08:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:09:00.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:09:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:09:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:09:01.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:09:01.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:09:01.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:09:01.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:09:01.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:09:03.175 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A323D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:09:03.180 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:09:03.180 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:09:03.190 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:09:03.191 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:09:03.200 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:09:07.386 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A2A290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:09:07.400 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:09:30.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:09:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:09:30.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:09:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:09:31.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:09:31.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:09:31.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:09:31.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:10:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:10:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:10:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:10:01.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:10:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:10:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:10:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:10:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:10:03.146 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A45AD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:10:03.151 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:10:03.151 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:10:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:10:03.162 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:10:03.171 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:10:07.372 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A55B50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:10:07.391 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:10:30.771 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:10:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:10:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:10:31.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:10:31.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:10:31.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:10:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:10:31.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:11:00.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:11:00.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:11:00.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:11:01.770 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:11:01.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:11:01.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:11:01.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:11:01.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:11:03.140 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4B8E10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:11:03.144 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:11:03.144 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:11:03.156 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:11:03.159 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:11:03.176 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:11:07.380 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A6A8D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:11:07.392 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:11:30.770 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:11:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:11:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:11:31.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:11:31.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:11:31.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:11:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:11:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:12:00.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:12:00.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:12:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:12:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:12:01.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:12:01.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:12:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:12:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:12:03.139 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A202D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:12:03.143 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:12:03.143 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:12:03.154 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:12:03.155 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:12:03.174 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:12:07.354 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A32290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:12:07.368 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:12:30.769 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:12:30.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:12:30.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:12:31.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:12:31.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:12:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:12:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:12:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:13:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:13:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:13:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:13:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:13:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:13:01.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:13:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:13:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:13:03.131 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59F6A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:13:03.136 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:13:03.136 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:13:03.146 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:13:03.146 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:13:03.153 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:13:07.358 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D9D9B3ED0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:13:07.372 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:13:30.770 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:13:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:13:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:13:31.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:13:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:13:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:13:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:13:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:14:00.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:14:00.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:14:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:14:01.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:14:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:14:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:14:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:14:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:14:03.169 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D9D9B2A50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:14:03.173 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:14:03.173 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:14:03.184 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:14:03.184 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:14:03.204 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:14:07.395 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A15150>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:14:07.412 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:14:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:14:30.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:14:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:14:31.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:14:31.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:14:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:14:31.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:14:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:15:00.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:15:00.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:15:00.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:15:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:15:01.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:15:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:15:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:15:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:15:03.150 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D9E648850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:15:03.153 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:15:03.153 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:15:03.166 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:15:03.168 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:15:03.184 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:15:07.379 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4B6310>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:15:07.392 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:15:30.771 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:15:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:15:30.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:15:31.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:15:31.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:15:31.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:15:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:15:31.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:16:00.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:16:00.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:16:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:16:01.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:16:01.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:16:01.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:16:01.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:16:01.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:16:03.129 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A23610>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:16:03.133 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:16:03.133 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:16:03.145 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:16:03.147 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:16:03.154 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:16:07.365 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A33CD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:16:07.378 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:16:30.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:16:30.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:16:30.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:16:31.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:16:31.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:16:31.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:16:31.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:16:31.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:17:00.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:17:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:17:00.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:17:01.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:17:01.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:17:01.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:17:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:17:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:17:03.130 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59E70D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:17:03.134 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:17:03.134 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:17:03.146 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:17:03.148 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:17:03.166 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:17:07.352 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4B9350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:17:07.380 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:17:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:17:30.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:17:30.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:17:31.773 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:17:31.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:17:31.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:17:31.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:17:31.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:18:00.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:18:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:18:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:18:01.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:18:01.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:18:01.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:18:01.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:18:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:18:03.158 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59CA290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:18:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:18:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:18:03.173 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:18:03.175 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:18:03.193 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:18:07.375 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A444D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:18:07.388 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:18:30.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:18:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:18:30.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:18:31.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:18:31.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:18:31.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:18:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:18:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:19:00.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:19:00.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:19:00.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:19:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:19:01.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:19:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:19:01.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:19:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:19:03.157 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A32290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:19:03.160 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:19:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:19:03.172 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:19:03.174 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:19:03.193 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:19:07.405 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A57C10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:19:07.417 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:19:30.772 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:19:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:19:30.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:19:31.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:19:31.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:19:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:19:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:19:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:20:00.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:20:00.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:20:00.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:20:01.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:20:01.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:20:01.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:20:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:20:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:20:03.135 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB58A5710>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:20:03.139 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:20:03.139 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:20:03.150 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:20:03.150 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:20:03.159 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:20:07.358 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A31490>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:20:07.371 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:20:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:20:30.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:20:30.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:20:31.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:20:31.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:20:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:20:31.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:20:31.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:21:00.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:21:00.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:21:00.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:21:01.774 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:21:01.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:21:01.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:21:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:21:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:21:03.159 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A476D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:21:03.163 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:21:03.163 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:21:03.175 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:21:03.176 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:21:03.195 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:21:07.393 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D9D9B0FD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:21:07.407 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:21:30.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:21:30.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:21:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:21:31.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:21:31.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:21:31.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:21:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:21:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:22:00.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:22:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:22:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:22:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:22:01.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:22:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:22:01.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:22:01.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:22:03.153 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59CA290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:22:03.158 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:22:03.158 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:22:03.169 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:22:03.169 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:22:03.189 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:22:07.367 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A23A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:22:07.380 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:22:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:22:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:22:30.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:22:31.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:22:31.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:22:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:22:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:22:31.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:23:00.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:23:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:23:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:23:01.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:23:01.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:23:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:23:01.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:23:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:23:03.134 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A15590>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:23:03.138 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:23:03.138 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:23:03.150 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:23:03.151 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:23:03.174 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:23:07.354 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4F5010>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:23:07.367 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:23:30.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:23:30.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:23:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:23:31.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:23:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:23:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:23:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:23:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:24:00.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:24:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:24:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:24:01.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:24:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:24:01.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:24:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:24:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:24:03.166 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A47D90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:24:03.170 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:24:03.171 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:24:03.186 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:24:03.187 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:24:03.204 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:24:07.438 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A30D10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:24:07.450 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:24:30.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:24:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:24:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:24:31.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:24:31.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:24:31.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:24:31.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:24:31.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:25:00.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:25:00.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:25:00.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:25:01.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:25:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:25:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:25:01.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:25:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:25:03.163 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A6A290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:25:03.167 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:25:03.167 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:25:03.178 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:25:03.179 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:25:03.200 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:25:07.379 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A13A50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:25:07.392 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:25:30.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:25:30.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:25:30.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:25:31.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:25:31.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:25:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:25:31.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:25:31.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:26:00.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:26:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:26:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:26:01.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:26:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:26:01.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:26:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:26:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:26:03.138 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A32610>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:26:03.142 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:26:03.142 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:26:03.156 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:26:03.459 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:26:03.459 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:26:07.644 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4F7090>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:26:07.658 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:26:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:26:30.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:26:30.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:26:31.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:26:31.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:26:31.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:26:31.819 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:26:31.819 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:27:00.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:27:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:27:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:27:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:27:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:27:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:27:01.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:27:01.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:27:03.138 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A4D410>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:27:03.142 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:27:03.142 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:27:03.154 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:27:03.156 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:27:03.163 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:27:07.409 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4BC610>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:27:07.422 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:27:30.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:27:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:27:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:27:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:27:31.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:27:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:27:31.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:27:31.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:28:00.775 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:28:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:28:00.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:28:01.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:28:01.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:28:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:28:01.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:28:01.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:28:03.144 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59FA290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:03.147 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:28:03.147 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:28:03.158 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:28:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:28:03.169 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:28:07.348 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A6A290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:07.360 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:28:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:28:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:28:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:28:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:28:31.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:28:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:28:31.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:28:31.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:28:35.562 | DEBUG    | wxauto_mgt.web.routes:check_auth:17 | 认证检查: password_required=False
2025-07-29 20:28:36.099 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:28:36.099 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:28:40.201 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD52D710>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:40.206 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:28:40.206 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:28:40.227 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:28:44.432 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4E5090>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:44.442 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:28:48.619 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4E6B10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:48.622 | DEBUG    | wxauto_mgt.web.routes:check_auth:17 | 认证检查: password_required=False
2025-07-29 20:28:48.627 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:28:48.627 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:28:48.632 | DEBUG    | wxauto_mgt.web.routes:check_auth:17 | 认证检查: password_required=False
2025-07-29 20:28:48.646 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:28:52.863 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4BD750>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:52.878 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:28:54.895 | DEBUG    | wxauto_mgt.web.api:get_instance_status:296 | 获取实例状态 API 被调用，参数：instance_id=wxauto_d3d71ec7
2025-07-29 20:28:54.898 | DEBUG    | wxauto_mgt.web.api:get_system_resources:381 | 获取系统资源 API 被调用，参数：instance_id=wxauto_d3d71ec7
2025-07-29 20:28:54.902 | DEBUG    | wxauto_mgt.web.api:get_instance_status:324 | 向实例 wxauto_d3d71ec7 发送状态请求: http://localhost:5000/api/health
2025-07-29 20:28:58.962 | WARNING  | wxauto_mgt.web.api:get_instance_status:356 | 获取实例 wxauto_d3d71ec7 状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD542250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:28:58.973 | WARNING  | wxauto_mgt.web.api:get_instance_status:357 | Traceback (most recent call last):
  File "urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "urllib3\connection.py", line 494, in request
    self.endheaders()
  File "http\client.py", line 1298, in endheaders
  File "http\client.py", line 1058, in _send_output
  File "http\client.py", line 996, in send
  File "urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
                ^^^^^^^^^^^^^^^^
  File "urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x0000028DBD542250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD542250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "web\api.py", line 325, in get_instance_status
  File "requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD542250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

2025-07-29 20:28:58.976 | DEBUG    | wxauto_mgt.web.api:get_system_resources:409 | 向实例 wxauto_d3d71ec7 发送健康状态请求: http://localhost:5000/api/health
2025-07-29 20:29:00.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:29:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:29:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:29:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:29:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:29:01.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:29:01.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:29:01.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:29:03.051 | WARNING  | wxauto_mgt.web.api:get_system_resources:417 | 获取实例 wxauto_d3d71ec7 健康状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A6A290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:03.052 | DEBUG    | wxauto_mgt.web.api:get_system_resources:420 | 向实例 wxauto_d3d71ec7 发送资源请求: http://localhost:5000/api/system/resources
2025-07-29 20:29:07.136 | WARNING  | wxauto_mgt.web.api:get_system_resources:495 | 获取实例 wxauto_d3d71ec7 资源使用情况失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/system/resources (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD580D10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:07.139 | WARNING  | wxauto_mgt.web.api:get_system_resources:496 | Traceback (most recent call last):
  File "urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
  File "urllib3\connection.py", line 494, in request
    self.endheaders()
  File "http\client.py", line 1298, in endheaders
  File "http\client.py", line 1058, in _send_output
  File "http\client.py", line 996, in send
  File "urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
                ^^^^^^^^^^^^^^^^
  File "urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x0000028DBD580D10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "requests\adapters.py", line 667, in send
    resp = conn.urlopen(
           ^^^^^^^^^^^^^
  File "urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/system/resources (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD580D10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "web\api.py", line 421, in get_system_resources
  File "requests\api.py", line 73, in get
    return request("get", url, params=params, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/system/resources (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD580D10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))

2025-07-29 20:29:07.140 | DEBUG    | wxauto_mgt.web.routes:check_auth:17 | 认证检查: password_required=False
2025-07-29 20:29:07.252 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:29:07.252 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:29:07.259 | DEBUG    | wxauto_mgt.web.routes:check_auth:17 | 认证检查: password_required=False
2025-07-29 20:29:11.369 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A16AD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:11.374 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:29:11.375 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:29:11.397 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:15.589 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD5404D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:15.606 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:30.776 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:29:30.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:29:30.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:29:31.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:29:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:29:31.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:29:31.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:29:31.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:29:32.000 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD580E90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:32.004 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:29:32.004 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:29:32.020 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:38.135 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4B8050>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:38.148 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:39.052 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:29:39.053 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:29:43.151 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4BDB10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:43.164 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:48.157 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4E4850>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:48.171 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:53.143 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A106D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:53.147 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:29:53.147 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:29:53.180 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:29:58.120 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD542FD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:29:58.134 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:00.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:30:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:30:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:30:01.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:30:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:30:01.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:30:01.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:30:01.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:30:03.174 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4E5010>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:03.178 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:03.178 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:03.195 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:03.195 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:03.211 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:07.429 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD583910>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:07.441 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:11.662 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD59E810>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:11.666 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:11.666 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:11.687 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:15.905 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028D9D9B2290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:15.917 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:20.089 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD584C90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:20.093 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:20.093 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:20.110 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:20.110 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:20.116 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:21.092 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 14824)
2025-07-29 20:30:24.326 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A33A10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:24.331 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:24.332 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:24.350 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:28.588 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD59F250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:28.604 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:30:30.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:30:30.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:30:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:30:31.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:30:31.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:30:31.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:30:31.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:30:32.812 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A55E90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:32.816 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:32.816 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:32.832 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:32.833 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:32.851 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:37.062 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4E7DD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:37.075 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:40.217 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_qrcode_instance_async:861 | 开始获取实例登录二维码: wxauto_d3d71ec7
2025-07-29 20:30:41.229 | ERROR    | wxauto_mgt.ui.components.instance_manager_panel:_qrcode_instance_async:880 | 获取实例二维码失败: wxauto_d3d71ec7, 错误: API错误 [-1]: Connection timeout to host http://localhost:5000/api/auxiliary/login/qrcode
2025-07-29 20:30:41.274 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A6A290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:41.278 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:41.278 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:41.296 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:41.296 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:41.319 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:43.986 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_auto_login_instance_async:788 | 开始自动登录实例: wxauto_d3d71ec7
2025-07-29 20:30:44.985 | ERROR    | wxauto_mgt.ui.components.instance_manager_panel:_auto_login_instance_async:814 | 自动登录实例失败: wxauto_d3d71ec7, 错误: API错误 [-1]: Connection timeout to host http://localhost:5000/api/auxiliary/login/auto
2025-07-29 20:30:45.498 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD3E5210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:45.509 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:49.736 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5A33C10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:49.740 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:49.740 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:49.758 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:50.889 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:50.893 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:51.882 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:51.886 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:52.903 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:52.914 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:53.884 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:53.889 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:53.968 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4E5090>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:53.971 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:30:53.971 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:30:54.004 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:54.886 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:54.890 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:55.895 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:55.901 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:56.910 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:56.915 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:57.912 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:57.917 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:58.198 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD3E6650>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:30:58.231 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:30:58.914 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:58.919 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:30:59.924 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:30:59.928 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:31:00.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:31:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:31:00.922 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:00.926 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:01.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:31:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:31:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:31:01.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:31:01.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:31:01.921 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:01.925 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:02.436 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD59E250>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:02.440 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:31:02.440 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:31:02.914 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:02.918 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:03.913 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:03.919 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:04.913 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:04.916 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:05.915 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:05.919 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:06.669 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD59CF50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:06.887 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:31:06.888 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:31:06.912 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:06.915 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:07.910 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:07.914 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:08.908 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:08.912 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:09.920 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:09.923 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:10.919 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:10.922 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:10.988 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD585510>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:10.998 | INFO     | wxauto_mgt.ui.main_window:_toggle_message_listener_async:674 | 消息监听已停止
2025-07-29 20:31:11.501 | DEBUG    | wxauto_mgt.ui.main_window:_refresh_message_panel:736 | 已通知消息面板刷新监听列表
2025-07-29 20:31:11.515 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_d3d71ec7 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-29 20:31:12.521 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:12.523 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:13.519 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:13.522 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:14.521 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:14.523 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:15.074 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD588810>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:15.192 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:15.521 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:15.524 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:16.520 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:16.523 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:17.523 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:17.528 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:18.527 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:18.531 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:19.391 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD589110>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:19.522 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:19.524 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:20.521 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:20.524 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:21.521 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:21.525 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:22.531 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:22.534 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:23.487 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD58BF90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:23.490 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:23.495 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:23.527 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:23.530 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:23.603 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:24.525 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:24.530 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:25.521 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:25.524 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:26.522 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:26.525 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:27.535 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:27.539 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:27.712 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD3FA910>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:27.724 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:31:27.724 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:31:27.838 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:27.839 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:27.843 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:28.547 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:28.550 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:29.553 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:29.556 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:30.555 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:30.559 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:30.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:31:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:31:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_d3d71ec7
2025-07-29 20:31:31.555 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:31.560 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:31:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_d3d71ec7 在数据库中有 3 条消息记录
2025-07-29 20:31:31.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_d3d71ec7 有 1 个监听对象
2025-07-29 20:31:31.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_d3d71ec7 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:31:31.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_d3d71ec7
2025-07-29 20:31:32.140 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB5905990>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:32.552 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:32.555 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:33.562 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:33.565 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:34.560 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:34.563 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:35.558 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:35.561 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:36.248 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4B9A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:36.550 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:36.553 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:37.562 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:37.565 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:38.574 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:38.578 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:39.570 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:39.574 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:40.339 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD580290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:40.564 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:40.579 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:40.582 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:41.594 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:41.598 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:42.584 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:42.587 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:43.601 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:43.609 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:44.593 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:44.596 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:44.662 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59C9C50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:44.673 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:31:44.673 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:31:45.591 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:45.594 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:46.587 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:46.590 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:47.586 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:47.591 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:48.587 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:48.591 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:48.954 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD583F90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:48.960 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:31:49.609 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:49.614 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:50.610 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:50.613 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:51.617 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:51.621 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:52.616 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:52.621 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:53.060 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD58A710>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:53.616 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:53.621 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:54.541 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_delete_instance_async:570 | 正在停止与实例 wxauto_d3d71ec7 相关的异步任务...
2025-07-29 20:31:54.541 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_delete_instance_async:577 | 当前有 3 个正在运行的异步任务
2025-07-29 20:31:54.543 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_delete_instance_async:592 | 已取消 0 个与实例 wxauto_d3d71ec7 相关的任务
2025-07-29 20:31:54.628 | DEBUG    | wxauto_mgt.ui.components.message_panel:_get_messages:1995 | 获取消息: 实例=wxauto_d3d71ec7, 聊天=Jooo
2025-07-29 20:31:54.632 | INFO     | wxauto_mgt.ui.components.message_panel:_get_messages:2016 | 获取到新消息: 实例=wxauto_d3d71ec7, 聊天=Jooo, 数量=3
2025-07-29 20:31:55.050 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_delete_instance_async:600 | 已从API客户端管理器中移除实例: wxauto_d3d71ec7
2025-07-29 20:31:55.085 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_delete_instance_async:604 | 已执行垃圾回收
2025-07-29 20:31:55.127 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_delete_instance_async:610 | 成功删除实例: wxauto_d3d71ec7
2025-07-29 20:31:55.144 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:547 | 执行查询: SELECT * FROM instances WHERE enabled = 1
2025-07-29 20:31:55.146 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:31:55.171 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:549 | 查询结果: 0 个实例
2025-07-29 20:31:55.171 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:554 | 执行查询: SELECT * FROM instances
2025-07-29 20:31:55.175 | WARNING  | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1272 | 未找到实例
2025-07-29 20:31:55.175 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_clear_instance_panels:1328 | 清除所有实例面板
2025-07-29 20:31:55.177 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:556 | 查询结果: 0 个实例
2025-07-29 20:31:55.177 | WARNING  | wxauto_mgt.ui.components.instance_card_list:refresh_instances:567 | 没有找到任何实例
2025-07-29 20:31:55.637 | WARNING  | wxauto_mgt.ui.components.message_panel:_auto_refresh:1769 | 实例 wxauto_d3d71ec7 不存在，清除选中的监听对象
2025-07-29 20:31:57.280 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB598B0D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:31:57.284 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 1
2025-07-29 20:32:00.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:32:00.782 | WARNING  | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1272 | 未找到实例
2025-07-29 20:32:00.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_clear_instance_panels:1328 | 清除所有实例面板
2025-07-29 20:32:01.380 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_d3d71ec7 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD580C50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:32:01.385 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:01.385 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:01.386 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.391 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.505 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.508 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.517 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.519 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:01.519 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:01.526 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:01.526 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:01.543 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:01.543 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:01.549 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:01.549 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:01.661 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.683 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.808 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:01.936 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:02.067 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:02.199 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:02.331 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:02.461 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:06.070 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:11.074 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:11.089 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:11.090 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:12.955 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 2292)
2025-07-29 20:32:16.072 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:21.050 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:21.051 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:21.065 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:26.070 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:32:30.787 | WARNING  | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1272 | 未找到实例
2025-07-29 20:32:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_clear_instance_panels:1328 | 清除所有实例面板
2025-07-29 20:32:31.071 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:31.084 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:31.084 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:36.073 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:41.053 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:41.053 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:41.074 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:46.072 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:32:59.368 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:32:59.368 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:32:59.401 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:33:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:33:00.800 | WARNING  | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1272 | 未找到实例
2025-07-29 20:33:00.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_clear_instance_panels:1328 | 清除所有实例面板
2025-07-29 20:33:11.234 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 8600)
2025-07-29 20:33:30.778 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:33:30.786 | WARNING  | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1272 | 未找到实例
2025-07-29 20:33:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_clear_instance_panels:1328 | 清除所有实例面板
2025-07-29 20:33:33.455 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_local_instance:1068 | 开始添加本机实例: {'instance_id': 'wxauto_c032f6ba', 'name': '本机', 'base_url': 'http://localhost:5000', 'api_key': 'test-key-2', 'enabled': True, 'config': {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}}
2025-07-29 20:33:33.473 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1427 | 开始添加实例: {'instance_id': 'wxauto_c032f6ba', 'name': '本机', 'base_url': 'http://localhost:5000', 'api_key': 'test-key-2', 'enabled': True, 'config': {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}}
2025-07-29 20:33:33.477 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1476 | 尝试直接使用SQL插入实例数据...
2025-07-29 20:33:33.477 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1504 | 执行SQL: INSERT INTO instances (instance_id, name, base_url, api_key, status, enabled, created_at, updated_at, config) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 20:33:33.477 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1505 | 参数值: ['wxauto_c032f6ba', '本机', 'http://localhost:5000', 'test-key-2', 'inactive', 1, 1753792413, 1753792413, '{"timeout": 30, "retry_limit": 3, "poll_interval": 1, "timeout_minutes": 30}']
2025-07-29 20:33:33.509 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1508 | 直接SQL插入成功: wxauto_c032f6ba
2025-07-29 20:33:33.515 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1517 | 插入的记录ID: 5
2025-07-29 20:33:33.515 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1557 | 已添加实例到API客户端: wxauto_c032f6ba
2025-07-29 20:33:33.516 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1566 | 已发送实例添加信号: wxauto_c032f6ba
2025-07-29 20:33:33.516 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_instance_async:1579 | 添加实例完成: 本机 (wxauto_c032f6ba)
2025-07-29 20:33:33.517 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_add_local_instance:1088 | 本机实例添加完成: wxauto_c032f6ba
2025-07-29 20:33:33.535 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:547 | 执行查询: SELECT * FROM instances WHERE enabled = 1
2025-07-29 20:33:33.536 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:33:33.563 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:549 | 查询结果: 1 个实例
2025-07-29 20:33:33.564 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:571 | 加载了 1 个实例
2025-07-29 20:33:33.565 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:575 | 实例 1: ID=wxauto_c032f6ba, 名称=本机
2025-07-29 20:33:33.570 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:594 | 已添加实例卡片: wxauto_c032f6ba
2025-07-29 20:33:33.589 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1313 | 创建实例面板: wxauto_c032f6ba
2025-07-29 20:33:33.589 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:33:33.593 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:33:34.597 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:33:34.601 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:33:34.604 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:33:34.608 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:33:34.608 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:33:37.363 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 3292)
2025-07-29 20:34:00.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:34:00.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:34:00.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:34:01.779 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:34:01.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:34:01.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:34:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:34:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:34:03.157 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_c032f6ba 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD59A290>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:34:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:34:03.161 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:34:03.177 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:34:05.834 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 9388)
2025-07-29 20:34:30.777 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:34:30.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:34:30.782 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:34:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:34:31.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:34:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:34:31.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:34:31.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:34:59.370 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:34:59.371 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:35:00.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:35:00.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:35:00.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:35:01.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:35:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:35:01.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:35:01.818 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:35:01.818 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:35:03.479 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_c032f6ba 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DBD4BAED0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:35:03.493 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:35:30.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:35:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:35:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:35:31.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:35:31.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:35:31.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:35:31.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:35:31.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:36:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:36:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:36:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:36:01.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:36:01.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:36:01.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:36:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:36:01.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:36:03.140 | WARNING  | wxauto_mgt.web.api:get_system_status:619 | 检查实例 wxauto_c032f6ba 连接状态失败: HTTPConnectionPool(host='localhost', port=5000): Max retries exceeded with url: /api/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028DB59F6990>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-29 20:36:03.143 | DEBUG    | wxauto_mgt.web.api:get_messages:1563 | 获取消息列表 API 被调用，参数：instance_id=None, chat_name=None, limit=10, since=None
2025-07-29 20:36:03.143 | DEBUG    | wxauto_mgt.web.api:get_messages:1585 | 执行消息查询：SELECT * FROM messages WHERE 1=1 ORDER BY create_time DESC LIMIT ? 参数：[10]
2025-07-29 20:36:03.154 | DEBUG    | wxauto_mgt.web.api:get_system_status:670 | 获取到实际监听对象数量: 0
2025-07-29 20:36:07.587 | INFO     | wxauto_mgt.ui.components.instance_manager_panel:_qrcode_instance_async:861 | 开始获取实例登录二维码: wxauto_c032f6ba
2025-07-29 20:36:08.581 | ERROR    | wxauto_mgt.ui.components.instance_manager_panel:_qrcode_instance_async:880 | 获取实例二维码失败: wxauto_c032f6ba, 错误: API错误 [-1]: Connection timeout to host http://localhost:5000/api/auxiliary/login/qrcode
2025-07-29 20:36:11.453 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 15028)
2025-07-29 20:36:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:36:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:36:30.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:36:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:36:31.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:36:31.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:36:31.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:36:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:37:00.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:37:00.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:37:00.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:37:01.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:37:01.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:37:01.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:37:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:37:01.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:37:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:37:30.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:37:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:37:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:37:31.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:37:31.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:37:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:37:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:38:00.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:38:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:38:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:38:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:38:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:38:01.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:38:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:38:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:38:30.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:38:30.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:38:30.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:38:31.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:38:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:38:31.816 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:38:31.820 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:38:31.820 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:39:00.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:39:00.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:39:00.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:39:01.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:39:01.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:39:01.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:39:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:39:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:39:30.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:39:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:39:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:39:31.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:39:31.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:39:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:39:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:39:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:40:00.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:40:00.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:40:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:40:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:40:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:40:01.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:40:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:40:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:40:30.780 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:40:30.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:40:30.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:40:31.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:40:31.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:40:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:40:31.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:40:31.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:40:37.366 | INFO     | wxauto_mgt.ui.main_window:_toggle_listening_service_async:899 | 已暂停消息监听服务
2025-07-29 20:40:38.172 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:38.173 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:38.223 | INFO     | wxauto_mgt.ui.main_window:_toggle_listening_service_async:875 | 已恢复消息监听服务
2025-07-29 20:40:41.318 | INFO     | wxauto_mgt.ui.main_window:_toggle_listening_service_async:899 | 已暂停消息监听服务
2025-07-29 20:40:42.217 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:42.218 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:43.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:43.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:44.217 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:44.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:45.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:45.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:46.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:46.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:47.227 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:47.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:48.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:48.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:49.224 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:49.225 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:50.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:50.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:51.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:51.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:52.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:52.232 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:53.227 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:53.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:54.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:54.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:55.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:55.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:56.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:56.230 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:57.227 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:57.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:58.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:58.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:40:59.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:40:59.232 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:00.226 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:00.227 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:00.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:41:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:41:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:41:01.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:01.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:01.783 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:41:01.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:41:01.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:41:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:41:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:41:02.218 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:02.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:03.224 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:03.225 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:03.287 | INFO     | wxauto_mgt.ui.main_window:_start_api_client:952 | 已启动本地API客户端: C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\wxauto_http_api.exe (PID: 18756)
2025-07-29 20:41:04.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:04.230 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:05.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:05.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:06.218 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:06.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:07.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:07.223 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:08.223 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:08.225 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:09.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:09.223 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:10.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:10.223 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:11.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:11.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:12.230 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:12.230 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:13.227 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:13.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:14.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:14.224 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:15.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:15.223 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:16.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:16.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:17.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:17.232 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:18.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:18.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:19.218 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:19.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:20.232 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:20.233 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:21.217 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:21.218 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:22.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:22.233 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:23.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:23.233 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:24.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:24.230 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:25.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:25.232 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:26.226 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:26.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:27.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:27.223 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:28.217 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:28.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:29.231 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:29.232 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:30.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:30.229 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:30.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:41:30.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:41:30.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:41:31.226 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:31.228 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:41:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:41:31.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:41:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:41:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:41:32.224 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:32.225 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:33.218 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:33.219 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:34.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:34.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:35.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:35.222 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:36.220 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:36.221 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:37.233 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:37.234 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:38.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:38.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:39.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:39.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:40.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:40.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:41.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:41.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:42.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:42.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:43.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:43.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:44.234 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:44.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:45.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:45.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:46.243 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:46.244 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:47.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:47.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:48.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:48.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:49.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:49.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:50.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:50.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:51.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:51.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:52.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:52.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:53.245 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:53.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:54.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:54.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:55.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:55.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:56.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:56.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:57.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:57.242 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:58.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:58.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:41:59.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:41:59.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:00.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:00.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:00.781 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:42:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:42:00.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:42:01.234 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:01.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:01.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:42:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:42:01.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:42:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:42:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:42:02.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:02.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:03.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:03.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:04.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:04.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:05.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:05.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:06.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:06.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:07.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:07.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:08.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:08.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:09.243 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:09.244 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:10.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:10.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:11.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:11.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:12.234 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:12.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:13.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:13.249 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:14.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:14.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:15.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:15.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:16.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:16.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:17.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:17.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:18.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:18.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:19.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:19.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:20.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:20.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:21.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:21.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:22.244 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:22.245 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:23.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:23.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:24.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:24.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:25.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:25.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:26.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:26.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:27.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:27.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:28.249 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:28.249 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:29.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:29.249 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:30.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:30.242 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:42:30.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:42:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:42:31.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:31.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:31.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:42:31.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:42:31.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:42:31.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:42:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:42:32.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:32.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:33.234 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:33.235 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:34.246 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:34.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:35.242 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:35.244 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:36.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:36.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:37.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:37.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:38.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:38.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:39.247 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:39.248 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:40.245 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:40.245 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:41.240 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:41.241 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:42.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:42.239 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:43.242 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:43.244 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:44.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:44.238 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:45.236 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:45.237 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:46.249 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:46.250 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:47.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:47.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:48.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:48.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:49.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:49.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:50.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:50.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:51.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:51.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:52.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:52.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:53.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:53.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:54.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:54.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:55.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:55.265 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:56.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:56.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:57.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:57.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:58.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:58.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:42:59.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:42:59.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:00.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:00.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:43:00.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:43:00.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:43:01.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:01.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:43:01.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:43:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:43:01.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:43:01.815 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:43:02.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:02.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:03.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:03.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:04.249 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:04.250 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:05.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:05.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:06.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:06.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:07.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:07.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:08.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:08.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:09.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:09.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:10.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:10.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:11.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:11.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:12.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:12.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:13.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:13.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:14.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:14.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:15.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:15.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:16.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:16.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:17.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:17.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:18.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:18.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:19.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:19.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:20.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:20.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:21.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:21.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:22.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:22.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:23.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:23.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:24.250 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:24.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:25.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:25.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:26.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:26.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:27.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:27.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:28.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:28.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:29.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:29.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:30.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:30.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:30.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:43:30.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:43:30.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:43:31.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:31.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:31.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:43:31.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:43:31.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:43:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:43:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:43:32.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:32.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:33.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:33.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:34.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:34.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:35.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:35.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:36.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:36.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:37.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:37.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:38.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:38.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:39.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:39.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:40.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:40.265 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:41.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:41.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:42.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:42.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:43.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:43.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:44.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:44.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:45.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:45.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:46.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:46.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:47.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:47.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:48.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:48.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:49.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:49.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:50.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:50.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:51.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:51.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:52.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:52.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:53.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:53.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:54.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:54.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:55.265 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:55.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:56.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:56.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:57.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:57.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:58.250 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:58.251 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:43:59.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:43:59.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:00.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:00.259 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:00.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:44:00.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:44:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:44:01.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:01.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:01.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:44:01.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:44:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:44:01.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:44:01.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:44:02.265 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:02.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:03.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:03.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:04.257 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:04.258 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:05.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:05.254 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:06.252 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:06.253 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:07.263 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:07.264 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:08.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:08.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:09.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:09.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:10.260 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:10.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:11.261 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:11.262 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:12.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:12.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:13.255 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:13.256 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:14.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:14.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:15.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:15.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:16.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:16.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:17.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:17.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:18.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:18.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:19.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:19.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:20.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:20.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:21.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:21.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:22.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:22.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:23.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:23.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:24.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:24.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:25.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:25.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:26.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:26.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:27.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:27.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:28.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:28.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:29.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:29.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:30.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:30.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:30.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:44:30.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:44:30.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:44:31.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:31.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:44:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:44:31.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:44:31.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:44:31.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:44:32.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:32.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:33.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:33.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:34.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:34.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:35.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:35.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:36.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:36.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:37.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:37.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:38.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:38.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:39.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:39.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:40.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:40.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:41.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:41.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:42.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:42.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:43.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:43.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:44.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:44.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:45.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:45.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:46.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:46.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:47.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:47.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:48.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:48.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:49.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:49.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:50.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:50.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:51.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:51.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:52.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:52.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:53.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:53.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:54.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:54.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:55.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:55.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:56.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:56.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:57.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:57.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:58.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:58.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:44:59.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:44:59.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:00.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:00.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:45:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:45:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:45:01.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:01.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:01.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:45:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:45:01.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:45:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:45:01.809 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:45:02.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:02.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:03.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:03.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:04.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:04.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:05.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:05.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:06.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:06.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:07.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:07.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:08.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:08.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:09.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:09.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:10.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:10.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:11.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:11.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:12.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:12.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:13.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:13.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:14.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:14.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:15.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:15.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:16.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:16.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:17.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:17.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:18.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:18.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:19.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:19.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:20.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:20.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:21.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:21.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:22.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:22.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:23.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:23.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:24.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:24.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:25.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:25.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:26.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:26.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:27.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:27.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:28.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:28.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:29.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:29.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:30.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:30.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:30.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:45:30.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:45:30.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:45:31.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:31.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:45:31.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:45:31.820 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:45:31.825 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:45:31.826 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:45:32.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:32.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:33.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:33.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:34.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:34.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:35.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:35.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:36.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:36.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:37.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:37.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:38.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:38.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:39.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:39.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:40.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:40.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:41.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:41.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:42.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:42.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:43.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:43.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:44.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:44.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:45.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:45.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:46.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:46.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:47.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:47.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:48.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:48.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:49.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:49.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:50.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:50.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:51.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:51.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:52.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:52.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:53.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:53.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:54.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:54.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:55.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:55.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:56.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:56.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:57.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:57.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:58.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:58.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:45:59.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:45:59.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:00.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:00.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:00.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:46:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:46:00.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:46:01.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:01.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:46:01.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:46:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:46:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:46:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:46:02.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:02.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:03.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:03.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:04.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:04.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:05.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:05.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:06.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:06.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:07.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:07.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:08.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:08.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:09.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:09.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:10.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:10.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:11.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:11.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:12.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:12.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:13.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:13.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:14.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:14.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:15.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:15.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:16.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:16.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:17.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:17.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:18.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:18.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:19.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:19.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:20.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:20.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:21.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:21.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:22.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:22.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:23.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:23.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:24.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:24.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:25.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:25.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:26.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:26.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:27.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:27.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:28.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:28.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:29.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:29.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:30.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:30.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:46:30.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:46:30.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:46:31.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:31.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:46:31.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:46:31.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:46:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:46:31.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:46:32.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:32.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:33.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:33.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:34.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:34.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:35.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:35.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:36.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:36.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:37.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:37.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:38.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:38.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:39.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:39.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:40.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:40.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:41.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:41.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:42.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:42.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:43.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:43.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:44.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:44.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:45.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:45.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:46.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:46.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:47.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:47.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:48.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:48.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:49.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:49.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:50.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:50.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:51.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:51.274 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:52.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:52.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:53.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:53.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:54.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:54.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:55.266 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:55.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:56.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:56.281 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:57.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:57.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:58.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:58.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:46:59.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:46:59.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:00.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:00.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:47:00.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:47:00.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:47:01.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:01.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:01.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:47:01.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:47:01.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:47:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:47:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:47:02.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:02.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:03.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:03.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:04.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:04.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:05.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:05.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:06.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:06.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:07.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:07.277 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:08.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:08.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:09.272 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:09.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:10.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:10.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:11.267 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:11.268 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:12.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:12.280 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:13.278 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:13.279 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:14.275 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:14.276 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:15.270 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:15.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:16.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:16.273 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:17.269 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:17.271 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:18.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:18.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:19.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:19.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:20.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:20.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:21.282 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:21.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:22.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:22.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:23.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:23.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:24.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:24.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:25.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:25.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:26.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:26.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:27.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:27.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:28.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:28.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:29.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:29.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:30.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:30.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:30.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:47:30.797 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:47:30.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:47:31.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:31.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:47:31.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:47:31.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:47:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:47:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:47:32.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:32.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:33.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:33.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:34.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:34.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:35.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:35.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:36.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:36.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:37.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:37.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:38.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:38.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:39.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:39.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:40.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:40.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:41.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:41.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:42.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:42.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:43.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:43.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:44.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:44.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:45.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:45.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:46.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:46.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:47.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:47.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:48.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:48.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:49.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:49.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:50.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:50.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:51.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:51.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:52.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:52.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:53.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:53.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:54.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:54.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:55.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:55.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:56.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:56.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:57.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:57.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:58.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:58.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:47:59.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:47:59.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:00.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:00.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:00.784 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:48:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:48:00.790 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:48:01.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:01.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:48:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:48:01.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:48:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:48:01.810 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:48:02.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:02.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:03.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:03.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:04.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:04.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:05.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:05.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:06.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:06.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:07.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:07.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:08.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:08.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:09.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:09.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:10.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:10.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:11.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:11.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:12.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:12.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:13.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:13.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:14.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:14.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:15.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:15.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:16.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:16.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:17.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:17.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:18.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:18.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:19.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:19.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:20.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:20.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:21.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:21.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:22.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:22.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:23.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:23.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:24.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:24.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:25.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:25.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:26.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:26.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:27.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:27.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:28.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:28.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:29.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:29.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:30.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:30.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:48:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:48:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:48:31.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:31.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:48:31.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:48:31.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:48:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:48:31.813 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:48:32.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:32.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:33.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:33.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:34.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:34.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:35.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:35.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:36.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:36.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:37.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:37.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:38.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:38.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:39.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:39.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:40.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:40.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:41.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:41.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:42.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:42.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:43.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:43.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:44.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:44.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:45.296 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:45.297 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:46.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:46.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:47.287 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:47.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:48.286 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:48.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:49.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:49.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:50.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:50.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:51.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:51.285 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:52.292 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:52.293 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:53.288 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:53.289 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:54.283 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:54.284 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:55.294 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:55.295 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:56.290 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:56.291 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:57.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:57.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:58.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:58.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:48:59.306 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:48:59.307 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:00.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:00.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:00.785 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:49:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:49:00.789 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:49:01.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:01.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:01.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:49:01.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:49:01.795 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:49:01.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:49:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:49:02.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:02.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:03.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:03.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:04.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:04.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:05.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:05.304 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:06.301 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:06.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:07.298 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:07.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:08.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:08.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:09.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:09.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:10.307 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:10.308 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:11.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:11.306 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:12.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:12.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:13.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:13.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:14.308 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:14.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:15.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:15.304 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:16.301 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:16.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:17.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:17.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:18.308 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:18.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:19.304 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:19.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:20.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:20.307 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:21.301 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:21.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:22.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:22.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:23.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:23.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:24.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:24.306 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:25.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:25.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:26.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:26.301 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:27.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:27.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:28.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:28.313 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:29.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:29.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:30.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:30.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:30.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:49:30.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:49:30.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:49:31.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:31.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:31.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:49:31.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:49:31.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:49:31.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:49:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:49:32.314 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:32.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:33.313 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:33.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:34.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:34.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:35.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:35.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:36.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:36.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:37.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:37.301 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:38.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:38.313 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:39.307 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:39.308 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:40.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:40.306 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:41.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:41.304 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:42.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:42.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:43.301 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:43.302 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:44.313 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:44.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:45.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:45.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:46.305 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:46.306 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:47.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:47.313 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:48.309 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:48.310 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:49.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:49.304 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:50.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:50.303 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:51.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:51.313 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:52.311 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:52.312 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:53.304 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:53.306 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:54.299 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:54.300 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:55.314 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:55.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:56.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:56.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:57.322 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:57.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:58.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:58.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:49:59.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:49:59.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:00.322 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:00.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:00.794 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:50:00.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:50:00.800 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:50:01.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:01.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:01.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:50:01.807 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:50:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:50:01.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:50:01.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:50:02.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:02.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:03.314 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:03.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:04.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:04.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:05.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:05.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:06.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:06.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:07.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:07.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:08.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:08.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:09.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:09.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:10.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:10.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:11.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:11.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:12.314 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:12.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:13.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:13.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:14.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:14.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:15.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:15.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:16.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:16.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:17.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:17.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:18.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:18.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:19.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:19.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:20.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:20.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:21.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:21.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:22.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:22.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:23.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:23.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:24.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:24.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:25.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:25.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:26.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:26.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:27.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:27.322 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:28.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:28.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:29.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:29.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:30.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:30.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:30.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:50:30.803 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:50:30.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:50:31.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:31.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:50:31.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:50:31.821 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:50:31.824 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:50:31.825 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:50:32.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:32.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:33.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:33.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:34.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:34.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:35.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:35.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:36.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:36.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:37.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:37.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:38.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:38.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:39.314 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:39.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:40.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:40.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:41.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:41.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:42.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:42.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:43.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:43.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:44.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:44.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:45.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:45.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:46.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:46.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:47.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:47.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:48.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:48.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:49.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:49.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:50.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:50.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:51.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:51.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:52.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:52.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:53.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:53.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:54.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:54.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:55.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:55.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:56.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:56.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:57.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:57.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:58.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:58.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:50:59.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:50:59.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:00.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:00.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:00.787 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:51:00.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:51:00.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:51:01.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:01.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:01.788 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:51:01.792 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:51:01.796 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:51:01.801 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:51:01.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:51:02.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:02.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:03.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:03.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:04.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:04.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:05.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:05.322 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:06.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:06.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:07.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:07.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:08.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:08.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:09.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:09.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:10.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:10.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:11.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:11.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:12.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:12.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:13.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:13.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:14.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:14.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:15.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:15.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:16.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:16.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:17.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:17.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:18.326 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:18.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:19.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:19.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:20.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:20.322 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:21.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:21.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:22.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:22.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:23.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:23.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:24.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:24.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:25.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:25.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:26.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:26.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:27.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:27.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:28.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:28.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:29.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:29.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:30.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:30.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:30.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:51:30.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:51:30.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:51:31.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:31.316 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:31.814 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:51:31.818 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:51:31.821 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:51:31.825 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:51:31.825 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:51:32.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:32.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:33.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:33.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:34.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:34.321 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:35.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:35.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:36.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:36.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:37.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:37.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:38.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:38.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:39.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:39.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:40.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:40.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:41.322 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:41.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:42.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:42.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:43.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:43.320 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:44.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:44.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:45.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:45.330 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:46.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:46.327 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:47.323 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:47.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:48.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:48.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:49.315 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:49.317 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:50.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:50.318 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:51.328 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:51.329 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:52.324 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:52.325 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:53.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:53.319 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:54.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:54.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:55.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:55.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:56.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:56.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:57.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:57.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:58.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:58.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:51:59.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:51:59.347 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:00.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:00.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:00.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:52:00.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:52:00.805 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:52:01.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:01.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:01.817 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:52:01.821 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:52:01.824 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:52:01.829 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:52:01.829 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:52:02.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:02.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:03.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:03.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:04.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:04.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:05.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:05.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:06.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:06.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:07.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:07.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:08.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:08.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:09.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:09.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:10.346 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:10.347 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:11.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:11.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:12.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:12.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:13.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:13.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:14.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:14.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:15.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:15.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:16.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:16.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:17.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:17.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:18.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:18.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:19.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:19.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:20.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:20.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:21.331 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:21.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:22.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:22.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:23.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:23.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:24.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:24.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:25.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:25.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:26.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:26.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:27.346 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:27.349 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:28.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:28.342 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:29.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:29.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:30.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:30.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:30.786 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:52:30.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:52:30.791 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:52:31.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:31.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:31.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:52:31.802 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:52:31.806 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:52:31.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:52:31.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:52:32.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:32.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:33.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:33.346 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:34.342 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:34.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:35.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:35.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:36.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:36.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:37.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:37.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:38.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:38.342 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:39.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:39.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:40.346 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:40.348 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:41.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:41.342 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:42.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:42.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:43.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:43.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:44.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:44.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:45.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:45.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:46.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:46.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:47.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:47.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:48.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:48.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:49.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:49.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:50.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:50.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:51.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:51.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:52.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:52.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:53.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:53.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:54.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:54.346 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:55.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:55.347 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:56.342 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:56.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:57.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:57.341 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:58.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:58.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:52:59.345 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:52:59.346 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:00.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:00.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:00.793 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 20:53:00.798 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 20:53:00.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 20:53:01.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:01.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:01.799 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 20:53:01.804 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 20:53:01.808 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 20:53:01.811 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 20:53:01.812 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 20:53:02.342 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:02.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:03.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:03.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:04.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:04.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:05.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:05.340 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:06.337 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:06.338 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:07.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:07.339 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:08.335 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:08.336 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:09.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:09.334 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:10.332 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:10.333 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 20:53:11.343 | DEBUG    | wxauto_mgt.ui.components.message_panel:_update_countdown:2922 | 监听服务已暂停，跳过更新倒计时
2025-07-29 20:53:11.344 | DEBUG    | wxauto_mgt.ui.components.message_panel:_auto_refresh:1689 | 监听服务已暂停，跳过自动刷新
2025-07-29 21:35:46.364 | INFO     | wxauto_mgt.utils.logging:setup_logging:80 | 日志系统初始化完成，日志文件：C:\Users\<USER>\Desktop\Windows_x64_2.0.0.1\Windows_x64_2.0.0\data\logs\wxauto_mgt_20250729.log
2025-07-29 21:35:46.364 | INFO     | __main__:init_services:163 | 正在初始化SSL配置...
2025-07-29 21:35:46.365 | INFO     | wxauto_mgt.utils.ssl_config:init_ssl:107 | 初始化SSL配置...
2025-07-29 21:35:46.365 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:27 | 使用certifi证书: C:\Users\<USER>\AppData\Local\Temp\_MEI31242\certifi\cacert.pem
2025-07-29 21:35:46.365 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:55 | SSL证书路径已设置: C:\Users\<USER>\AppData\Local\Temp\_MEI31242\certifi\cacert.pem
2025-07-29 21:35:46.887 | INFO     | wxauto_mgt.utils.ssl_config:configure_ssl:64 | SSL上下文创建成功
2025-07-29 21:35:53.798 | INFO     | wxauto_mgt.utils.ssl_config:verify_ssl_setup:91 | SSL连接测试成功: https://httpbin.org/get
2025-07-29 21:35:53.799 | INFO     | wxauto_mgt.utils.ssl_config:init_ssl:116 | SSL配置和验证完成
2025-07-29 21:35:53.800 | INFO     | __main__:init_services:166 | SSL配置初始化成功
2025-07-29 21:35:53.952 | INFO     | __main__:init_services:178 | 正在初始化配置管理器...
2025-07-29 21:35:54.017 | INFO     | __main__:init_services:181 | 配置管理器初始化完成
2025-07-29 21:35:54.018 | INFO     | __main__:init_services:184 | 正在加载实例配置...
2025-07-29 21:35:54.024 | INFO     | __main__:init_services:197 | 从数据库中获取到 1 个实例
2025-07-29 21:35:54.025 | INFO     | __main__:init_services:216 | 正在加载实例: wxauto_c032f6ba (本机)
2025-07-29 21:35:54.025 | INFO     | __main__:init_services:218 | 已加载实例: wxauto_c032f6ba
2025-07-29 21:35:54.025 | INFO     | __main__:init_services:232 | 正在启动消息监听...
2025-07-29 21:35:54.033 | INFO     | __main__:init_services:234 | 消息监听服务已启动
2025-07-29 21:35:54.033 | INFO     | __main__:init_services:244 | 正在初始化消息投递服务...
2025-07-29 21:35:54.057 | INFO     | __main__:init_services:248 | 正在启动消息投递服务...
2025-07-29 21:35:54.058 | INFO     | __main__:init_services:250 | 消息投递服务已启动
2025-07-29 21:35:54.058 | INFO     | __main__:init_services:255 | 服务初始化完成
2025-07-29 21:35:54.112 | DEBUG    | wxauto_mgt.ui.components.message_panel:__init__:141 | 日志处理器初始化完成，关键事件过滤词已设置
2025-07-29 21:35:54.113 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2722 | 消息监听界面已启动，日志系统已连接
2025-07-29 21:35:54.114 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2728 | 当前监听对象数量: 0
2025-07-29 21:35:54.115 | INFO     | wxauto_mgt.ui.components.message_panel:_init_logging:2729 | 轮询间隔: 5秒, 超时时间: 30分钟
2025-07-29 21:35:54.115 | DEBUG    | wxauto_mgt.ui.components.message_panel:_init_logging:2734 | 日志系统初始化完成
2025-07-29 21:35:54.116 | DEBUG    | wxauto_mgt.ui.components.message_panel:__init__:886 | 消息监听面板已初始化
2025-07-29 21:35:54.123 | DEBUG    | wxauto_mgt.web.config:_load_from_store:62 | 使用默认Web服务配置
2025-07-29 21:35:54.124 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:294 | 已设置端口号: 8080
2025-07-29 21:35:54.125 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:300 | 已设置主机地址: 0.0.0.0
2025-07-29 21:35:54.125 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:305 | 已设置自动启动: True
2025-07-29 21:35:54.125 | INFO     | wxauto_mgt.ui.components.web_service_panel:_load_web_service_config:318 | Web服务面板已加载配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 21:35:54.130 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:284 | 事件循环未运行，无法获取实例名称
2025-07-29 21:35:54.132 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:547 | 执行查询: SELECT * FROM instances WHERE enabled = 1
2025-07-29 21:35:54.133 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 21:35:54.135 | INFO     | wxauto_mgt.ui.main_window:__init__:56 | 设置延迟配置保存定时器
2025-07-29 21:35:54.136 | INFO     | wxauto_mgt.ui.utils.ui_monitor:start_monitoring:58 | UI响应性监控已启动，检查间隔: 100ms
2025-07-29 21:35:54.137 | INFO     | wxauto_mgt.ui.main_window:__init__:62 | 主窗口已初始化
2025-07-29 21:35:54.353 | DEBUG    | __main__:main:396 | 当前平台不支持add_signal_handler，跳过信号处理设置
2025-07-29 21:35:54.354 | INFO     | __main__:main:400 | 程序已启动
2025-07-29 21:35:54.358 | INFO     | wxauto_mgt.ui.components.message_panel:_update_status_count:1976 | 消息统计: 已处理: 0, 未处理: 0, 总计: 0
2025-07-29 21:35:54.358 | INFO     | wxauto_mgt.ui.components.message_panel:_update_status_count:1976 | 消息统计: 已处理: 0, 未处理: 0, 总计: 0
2025-07-29 21:35:54.397 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1313 | 创建实例面板: wxauto_c032f6ba
2025-07-29 21:35:54.397 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 21:35:54.403 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 21:35:54.404 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:549 | 查询结果: 1 个实例
2025-07-29 21:35:54.404 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:571 | 加载了 1 个实例
2025-07-29 21:35:54.405 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:575 | 实例 1: ID=wxauto_c032f6ba, 名称=本机
2025-07-29 21:35:54.923 | WARNING  | wxauto_mgt.ui.components.delivery_rule_panel:_get_instance_name:271 | 获取实例名称超时: wxauto_c032f6ba
2025-07-29 21:35:55.936 | DEBUG    | wxauto_mgt.ui.components.instance_card_list:refresh_instances:594 | 已添加实例卡片: wxauto_c032f6ba
2025-07-29 21:35:55.943 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 21:35:55.973 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 21:35:55.973 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:139 | 实例 wxauto_c032f6ba 正在更新中，跳过
2025-07-29 21:35:55.975 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_c032f6ba 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-29 21:35:56.109 | INFO     | wxauto_mgt.ui.components.service_platform_panel:refresh_platforms:130 | 开始刷新服务平台列表...
2025-07-29 21:35:56.109 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh_platforms:177 | 启动异步刷新线程...
2025-07-29 21:35:56.110 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:run_async_task:137 | 创建新的事件循环...
2025-07-29 21:35:56.111 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:143 | 开始异步刷新任务...
2025-07-29 21:35:56.112 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:156 | 获取所有平台...
2025-07-29 21:35:56.115 | INFO     | wxauto_mgt.ui.components.service_platform_panel:refresh:158 | 获取到 2 个平台
2025-07-29 21:35:56.116 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:refresh:161 | 发出platforms_loaded信号...
2025-07-29 21:35:56.118 | DEBUG    | wxauto_mgt.ui.components.service_platform_panel:run_async_task:166 | 异步刷新任务完成
2025-07-29 21:35:56.126 | INFO     | wxauto_mgt.ui.components.service_platform_panel:_update_platform_table:197 | 刷新平台列表成功，共 2 个平台
2025-07-29 21:35:56.132 | INFO     | wxauto_mgt.ui.main_window:start_delayed_save:53 | 启动延迟配置保存任务（2秒后执行）
2025-07-29 21:35:56.133 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:354 | 开始执行延迟配置保存任务
2025-07-29 21:35:56.133 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:357 | 正在获取实例配置...
2025-07-29 21:35:56.137 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:368 | 正在获取Web服务配置...
2025-07-29 21:35:56.137 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:370 | 获取到Web服务配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 21:35:56.138 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:373 | 加载Web服务配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 21:35:56.138 | DEBUG    | wxauto_mgt.ui.main_window:_delayed_config_save:377 | 正在刷新Web服务面板UI...
2025-07-29 21:35:56.139 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:355 | 刷新端口号: 8080
2025-07-29 21:35:56.139 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:360 | 刷新主机地址: 0.0.0.0
2025-07-29 21:35:56.139 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:365 | 刷新自动启动: True
2025-07-29 21:35:56.141 | DEBUG    | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:373 | 刷新密码状态: 未设置
2025-07-29 21:35:56.142 | INFO     | wxauto_mgt.ui.components.web_service_panel:refresh_config_from_database:376 | Web服务面板已从数据库刷新配置: {'host': '0.0.0.0', 'port': 8080, 'auto_start': True}
2025-07-29 21:35:56.143 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:379 | 已更新Web服务面板UI配置
2025-07-29 21:35:56.143 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:385 | 检测到Web服务自动启动配置
2025-07-29 21:35:56.147 | INFO     | wxauto_mgt.web.config:save_config:197 | Web服务配置已保存: host=0.0.0.0, port=8080, auto_start=True
2025-07-29 21:35:56.498 | DEBUG    | wxauto_mgt.web.config:initialize:77 | Web服务配置初始化 - 数据库路径: C:\Users\<USER>\AppData\Local\Temp\_MEI31242\data\wxauto_mgt.db
2025-07-29 21:35:56.499 | DEBUG    | wxauto_mgt.web.config:initialize:82 | Web服务配置初始化 - 数据库文件存在
2025-07-29 21:35:56.503 | DEBUG    | wxauto_mgt.web.config:initialize:88 | Web服务配置初始化 - 从配置存储读取: {}
2025-07-29 21:35:56.503 | DEBUG    | wxauto_mgt.web.config:initialize:89 | Web服务配置初始化 - 配置类型: <class 'dict'>
2025-07-29 21:35:56.504 | DEBUG    | wxauto_mgt.web.config:initialize:93 | Web服务配置初始化 - 配置字段: []
2025-07-29 21:35:56.504 | DEBUG    | wxauto_mgt.web.config:initialize:94 | Web服务配置初始化 - 是否包含密码: False
2025-07-29 21:35:56.506 | ERROR    | wxauto_mgt.web.config:initialize:112 | Web服务配置初始化 - 数据库直接查询失败: no such table: configs
2025-07-29 21:35:56.506 | WARNING  | wxauto_mgt.web.config:initialize:116 | Web服务配置初始化 - 读取到空配置，尝试直接查询数据库
2025-07-29 21:35:56.508 | ERROR    | wxauto_mgt.web.config:initialize:131 | Web服务配置初始化 - 直接查询数据库失败: no such table: configs
2025-07-29 21:35:56.508 | DEBUG    | wxauto_mgt.web.config:_apply_config:157 | 已加载Web服务配置: host=0.0.0.0, port=8080, auto_start=True, has_password=False
2025-07-29 21:35:56.511 | INFO     | wxauto_mgt.web.server:create_app:74 | 静态文件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI31242\wxauto_mgt\web\static
2025-07-29 21:35:56.511 | INFO     | wxauto_mgt.web.server:create_app:86 | 模板文件目录: C:\Users\<USER>\AppData\Local\Temp\_MEI31242\wxauto_mgt\web\templates
2025-07-29 21:35:56.541 | INFO     | wxauto_mgt.web.routes:register_routes:57 | 注册Web路由
2025-07-29 21:35:56.543 | INFO     | wxauto_mgt.web.server:run_server:234 | Web服务器启动中，地址: http://0.0.0.0:8080
2025-07-29 21:35:56.561 | DEBUG    | wxauto_mgt.web.config:initialize:77 | Web服务配置初始化 - 数据库路径: C:\Users\<USER>\AppData\Local\Temp\_MEI31242\data\wxauto_mgt.db
2025-07-29 21:35:56.562 | DEBUG    | wxauto_mgt.web.config:initialize:82 | Web服务配置初始化 - 数据库文件存在
2025-07-29 21:35:56.564 | DEBUG    | wxauto_mgt.web.config:initialize:88 | Web服务配置初始化 - 从配置存储读取: {}
2025-07-29 21:35:56.564 | DEBUG    | wxauto_mgt.web.config:initialize:89 | Web服务配置初始化 - 配置类型: <class 'dict'>
2025-07-29 21:35:56.565 | DEBUG    | wxauto_mgt.web.config:initialize:93 | Web服务配置初始化 - 配置字段: []
2025-07-29 21:35:56.567 | DEBUG    | wxauto_mgt.web.config:initialize:94 | Web服务配置初始化 - 是否包含密码: False
2025-07-29 21:35:56.569 | ERROR    | wxauto_mgt.web.config:initialize:112 | Web服务配置初始化 - 数据库直接查询失败: no such table: configs
2025-07-29 21:35:56.569 | WARNING  | wxauto_mgt.web.config:initialize:116 | Web服务配置初始化 - 读取到空配置，尝试直接查询数据库
2025-07-29 21:35:56.571 | ERROR    | wxauto_mgt.web.config:initialize:131 | Web服务配置初始化 - 直接查询数据库失败: no such table: configs
2025-07-29 21:35:56.571 | DEBUG    | wxauto_mgt.web.config:_apply_config:157 | 已加载Web服务配置: host=0.0.0.0, port=8080, auto_start=True, has_password=False
2025-07-29 21:35:56.571 | INFO     | wxauto_mgt.web.server:startup_event:101 | Web服务配置初始化完成
2025-07-29 21:35:56.586 | INFO     | wxauto_mgt.web.security:initialize_security:40 | 已加载JWT密钥
2025-07-29 21:35:56.586 | INFO     | wxauto_mgt.web.server:startup_event:106 | 安全模块初始化完成
2025-07-29 21:35:57.544 | INFO     | wxauto_mgt.web:start_web_service:96 | Web服务已启动，地址: http://0.0.0.0:8080
2025-07-29 21:35:57.544 | INFO     | wxauto_mgt.ui.components.web_service_panel:_start_web_service:466 | Web服务已启动，地址: http://0.0.0.0:8080
2025-07-29 21:35:57.545 | INFO     | wxauto_mgt.ui.main_window:_delayed_config_save:398 | 延迟配置保存任务完成
2025-07-29 21:35:57.551 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 21:35:57.555 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 21:35:57.558 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 21:35:57.561 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 21:35:57.561 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
2025-07-29 21:35:57.569 | DEBUG    | wxauto_mgt.ui.components.message_panel:refresh_listeners:1289 | 从实例 wxauto_c032f6ba 加载配置: {'timeout': 30, 'retry_limit': 3, 'poll_interval': 1, 'timeout_minutes': 30}
2025-07-29 21:36:24.104 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1267 | 开始刷新状态...
2025-07-29 21:36:24.108 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:refresh_status:1318 | 刷新了 1 个实例的状态
2025-07-29 21:36:24.109 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:152 | 开始更新实例状态: wxauto_c032f6ba
2025-07-29 21:36:25.116 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:199 | 获取到健康状态信息: {'status': 'error', 'uptime': 0, 'wechat_status': 'disconnected'}
2025-07-29 21:36:25.120 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:220 | 状态面板检测到实例 wxauto_c032f6ba 在数据库中有 0 条消息记录
2025-07-29 21:36:25.123 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:230 | 状态面板检测到实例 wxauto_c032f6ba 有 0 个监听对象
2025-07-29 21:36:25.127 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:_handle_status_update:1409 | 更新了实例 wxauto_c032f6ba 的状态：状态=错误，运行时间=00:00:00
2025-07-29 21:36:25.127 | DEBUG    | wxauto_mgt.ui.components.instance_manager_panel:update_status:261 | 成功更新实例状态: wxauto_c032f6ba
