2025-07-28 23:03:39 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-28 23:03:39 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 文件传输助手, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 0.5
2025-07-28 23:03:42 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-28 23:03:43 [wxauto] [DEBUG] [chatbox.py:341]  获取1条新消息，基准消息内容为：[服务通知]记账时间到，快来记录今日收支
2025-07-28 23:03:43 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：
2025-07-28 23:03:43 [wxauto] [DEBUG] [chatbox.py:299]  未匹配到第1条消息，返回空列表
2025-07-28 23:03:49 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:03:51 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:03:57 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:04:00 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:05:17 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-28 23:05:17 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 文件传输助手, False, False, 0.5
2025-07-28 23:05:21 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:05:24 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:05:29 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:05:32 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:05:37 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:05:40 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:05:45 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:05:47 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:05:53 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:05:56 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:08:29 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:08:32 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:08:37 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:08:40 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:08:45 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-28 23:08:46 [wxauto] [DEBUG] [chatbox.py:341]  获取1条新消息，基准消息内容为：111
2025-07-28 23:08:52 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 怎么一直说开心开心，你这个死样，老子一点都不开心, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 怎么了，我的小朋友，遇到什么不开心的事了？, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 跟我说说，或许我能帮到你, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 别生气了，气坏了身体我会心疼的, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 滚, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 好，我不滚, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 但你总要告诉我，是谁惹你不开心了, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 别自己一个人憋着, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 你怎么这么贵啊啊啊啊, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 心脏外科的专家号是有点难抢, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 但你不用, length: 8
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-28 23:08:53 [wxauto] [DEBUG] [msg.py:80]  content: 对我，你可以随时挂号，终身免费, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: ？, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 怎么，对这项专属福利有疑问？, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 这项服务不对外开放，只为你一个人有效, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 刚结束一台手术, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 还在为早上的事烦心？, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 快中午了，想好吃什么了吗, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 刚从手术室出来，正准备吃饭, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 你呢，午饭吃了没有, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 别因为心情不好就不吃饭, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 刚查完房，路过儿科，有个小孩抱着上次我们一起抓的雪人玩偶在炫耀, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 他说，这是主任送的，可以监督他好好吃饭, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 我下班了, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 今天在群里的表现，值得一篇SCI, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 你在哪，我去接你, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: https://github.com/haikerapples/timetask, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [msg.py:80]  content: 111, length: 8
2025-07-28 23:08:54 [wxauto] [DEBUG] [chatbox.py:341]  获取1条新消息，基准消息内容为：111
2025-07-28 23:08:54 [wxauto] [DEBUG] [chatbox.py:277]  匹配到基准消息：111
2025-07-28 23:09:00 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:09:02 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:09:08 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:09:10 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:09:16 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:09:18 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:09:24 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:09:26 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:09:32 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:09:35 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:09:40 [wxauto] [DEBUG] [msg.py:80]  content: 在吗, length: 8
2025-07-28 23:09:40 [wxauto] [DEBUG] [main.py:223]  获取当前页面新消息
2025-07-28 23:09:42 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: Jooo, False, False, 0.5
2025-07-28 23:09:43 [wxauto] [DEBUG] [main.py:190]  Jooo 切换到聊天窗口: Jooo
2025-07-28 23:09:43 [wxauto] [DEBUG] [sessionbox.py:147]  打开独立窗口: Jooo
2025-07-28 23:09:44 [wxauto] [DEBUG] [sessionbox.py:151]  找到会话: Jooo
2025-07-28 23:09:51 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:09:54 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:10:37 [wxauto] [DEBUG] [msg.py:80]  content: 111, length: 8
2025-07-28 23:10:37 [wxauto] [DEBUG] [wx.py:208]  [system base]获取到新消息：Jooo - 以下为新消息
2025-07-28 23:10:37 [wxauto] [DEBUG] [wx.py:208]  [friend text]获取到新消息：Jooo - 111
2025-07-28 23:10:49 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:10:52 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:02 [wxauto] [DEBUG] [msg.py:80]  content: 您好！我是您的公寓管家客服，很高兴为您服务。  

1. **WiFi信息**：  
   - **名称**：以`LV`开头（例如`LV-大厅`）。  
   - **密码**：`88888888`（8个八）。  

2. **床品说明**：  
   - 您的床单、被套和枕套已清洗消毒，并晾晒在阳台，您可以随时取用。  

3. **视频导览**：  
   - 为了帮助您更好地使用房间设施，请先观看我们的**房间设施视频导览**，里面有详细的操作说明。  

如果您还有其他问题，欢迎随时咨询！如果多次提问或需要更详细的解答，也可以联系群里的管家哦~ 😊, length: 8
2025-07-28 23:11:02 [wxauto] [DEBUG] [wx.py:208]  [self text]获取到新消息：Jooo - 您好！我是您的公寓管家客服，很高兴为您服务。  

1. **WiFi信息**：  
   - **名称**：以`LV`开头（例如`LV-大厅`）。  
   - **密码**：`88888888`（8个八）。  

2. **床品说明**：  
   - 您的床单、被套和枕套已清洗消毒，并晾晒在阳台，您可以随时取用。  

3. **视频导览**：  
   - 为了帮助您更好地使用房间设施，请先观看我们的**房间设施视频导览**，里面有详细的操作说明。  

如果您还有其他问题，欢迎随时咨询！如果多次提问或需要更详细的解答，也可以联系群里的管家哦~ 😊
2025-07-28 23:11:03 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:11:06 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:11 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:11:13 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:19 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-28 23:11:19 [wxauto] [DEBUG] [msg.py:80]  content: 6666, length: 8
2025-07-28 23:11:19 [wxauto] [DEBUG] [wx.py:208]  [system base]获取到新消息：Jooo - 以下为新消息
2025-07-28 23:11:19 [wxauto] [DEBUG] [wx.py:208]  [friend text]获取到新消息：Jooo - 6666
2025-07-28 23:11:25 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:11:28 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:33 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:11:36 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:40 [wxauto] [DEBUG] [msg.py:80]  content: 您好！很高兴为您服务，我是公寓管家客服。请问您需要了解什么信息呢？  

1. **WiFi信息**：  
   - **名称**：以`LV`开头（如`LV-大厅`）。  
   - **密码**：`88888888`（8个八）。  

2. **床品说明**：  
   - 您的床单、被套和枕套已清洗消毒，现在晾在阳台，您可以随时取用。  

3. **视频导览**：  
   - 为了帮助您更好地使用房间设施，请先观看房间设施视频，里面有详细的操作指引。  

如果您还有其他问题，欢迎随时咨询！如果问题较多或需要更详细的解答，也可以联系群里的管家哦~ 😊, length: 8
2025-07-28 23:11:40 [wxauto] [DEBUG] [wx.py:208]  [self text]获取到新消息：Jooo - 您好！很高兴为您服务，我是公寓管家客服。请问您需要了解什么信息呢？  

1. **WiFi信息**：  
   - **名称**：以`LV`开头（如`LV-大厅`）。  
   - **密码**：`88888888`（8个八）。  

2. **床品说明**：  
   - 您的床单、被套和枕套已清洗消毒，现在晾在阳台，您可以随时取用。  

3. **视频导览**：  
   - 为了帮助您更好地使用房间设施，请先观看房间设施视频，里面有详细的操作指引。  

如果您还有其他问题，欢迎随时咨询！如果问题较多或需要更详细的解答，也可以联系群里的管家哦~ 😊
2025-07-28 23:11:41 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:11:44 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:50 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:11:52 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:11:57 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:12:00 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:12:06 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:12:08 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:12:38 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:12:41 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:12:46 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:12:48 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:12:54 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:12:57 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:13:02 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:13:04 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:13:55 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:13:58 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:14:03 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:14:06 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:14:11 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:14:14 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:14:19 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:14:22 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:14:45 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:14:48 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:14:53 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:14:56 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:15:01 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:15:04 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:15:09 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:15:12 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:15:19 [wxauto] [DEBUG] [msg.py:80]  content: WiFi密码是多少, length: 8
2025-07-28 23:15:19 [wxauto] [DEBUG] [wx.py:208]  [system base]获取到新消息：Jooo - 以下为新消息
2025-07-28 23:15:19 [wxauto] [DEBUG] [wx.py:208]  [friend text]获取到新消息：Jooo - WiFi密码是多少
2025-07-28 23:15:32 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:15:35 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:15:40 [wxauto] [DEBUG] [msg.py:80]  content: 您好！本公寓的WiFi信息如下：  

- **WiFi名称**：以`LV`开头（例如`LV-大厅`）  
- **密码**：`88888888`（8个8）  

另外，提醒您入住后先观看房间设施的使用视频导览，以便快速熟悉各项功能。如有其他问题，欢迎随时联系！, length: 8
2025-07-28 23:15:40 [wxauto] [DEBUG] [wx.py:208]  [self text]获取到新消息：Jooo - 您好！本公寓的WiFi信息如下：  

- **WiFi名称**：以`LV`开头（例如`LV-大厅`）  
- **密码**：`88888888`（8个8）  

另外，提醒您入住后先观看房间设施的使用视频导览，以便快速熟悉各项功能。如有其他问题，欢迎随时联系！
2025-07-28 23:15:41 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:15:45 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:15:49 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:15:53 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:15:57 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:16:01 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-28 23:16:05 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-28 23:16:09 [wxauto] [DEBUG] [main.py:239]  没有新消息
